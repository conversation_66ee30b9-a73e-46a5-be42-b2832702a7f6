<?php
/*
 * Copyright (c) 2023. Hi there I'm <PERSON><PERSON>.
 *
 */

namespace helpers;


class Utility
{
	public static function GenerateRandomString(): string
	{
		$characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
		$length = 7;
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, strlen($characters) - 1)];
		}
		
		return $randomString;
	}
	
	
	public static function validateAssetLinks($assetLinkJson, $sha, $domain, $package): bool|string
	{
		$domain = "https://$domain";
		if (count($assetLinkJson) !== 3) {
			return 'Invalid Asset Linking.';
		}
		$itemSha = $assetLinkJson[0]['target']['sha256_cert_fingerprints'][0] ?? null;
		$itemDomain = $assetLinkJson[1]['target']['site'] ?? null;
		$itemPackage = $assetLinkJ<PERSON>[0]['target']['package_name'] ?? null;
		if ($itemSha === $sha && $itemDomain === $domain && $itemPackage === $package) {
			return true;
		} else {
			$differences = [];
			if ($itemSha !== $sha) {
				$differences[] = "SHA mismatch";
			}
			if ($itemDomain !== $domain) {
				$differences[] = "Domain mismatch";
			}
			if ($itemPackage !== $package) {
				$differences[] = "Package mismatch";
			}
			
			return implode(', ', $differences);
		}
	}
}
