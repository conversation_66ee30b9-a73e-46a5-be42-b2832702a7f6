<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::table('projects', function (Blueprint $table) {
			$table->text('original_url')->nullable(true);
			$table->text('demo_url')->nullable(true);
		});
	}
	
	
	public function down(): void
	{
		Schema::table('projects', function (Blueprint $table) {
			$table->dropColumn('original_url');
			$table->dropColumn('demo_url');
		});
	}
};
