<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('skills', function (Blueprint $table) {
			$table->id();
			$table->string('name');
			$table->unsignedBigInteger('percent')->nullable(false)->default(0);
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('skills');
	}
};
