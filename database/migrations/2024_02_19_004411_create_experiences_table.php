<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('experiences', function (Blueprint $table) {
			$table->id();
			$table->string('name');
			$table->date('start');
			$table->date('finish')->nullable(true);
			$table->string('company')->nullable(true);
			$table->text('description')->nullable();
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('experiences');
	}
};
