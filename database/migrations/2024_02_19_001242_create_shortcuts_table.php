<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('shortcuts', function (Blueprint $table) {
			$table->id();
			$table->longText('original_url');
			$table->string('shortcut');
			$table->softDeletes();
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('shortcuts');
	}
};
