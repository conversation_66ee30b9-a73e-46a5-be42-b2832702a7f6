<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::table('tools', function (Blueprint $table) {
			$table->uuid()->after('id');
			$table->text('url')->after('description');
		});
	}
	
	
	public function down(): void
	{
		Schema::table('tools', function (Blueprint $table) {
			$table->dropColumn('uuid');
			$table->dropColumn('url');
		});
	}
};
