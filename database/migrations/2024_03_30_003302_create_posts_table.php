<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('posts', function (Blueprint $table) {
			$table->id();
			$table->string('title');
			$table->longText('description')->nullable();
			$table->json('hashtags')->nullable();
			$table->json('images')->nullable();
			$table->string('action')->nullable();
			$table->boolean('published')->default(false);
			$table->datetime('posting_date');
			$table->softDeletes();
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('posts');
	}
};
