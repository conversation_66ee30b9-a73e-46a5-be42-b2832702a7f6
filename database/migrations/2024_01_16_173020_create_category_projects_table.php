<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('category_project', function (Blueprint $table) {
			$table->id();
			$table->foreignId('category_id')->constrained('categories')->cascadeOnDelete();
			$table->foreignId('project_id')->constrained('projects')->cascadeOnDelete();
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('category_project');
	}
};
