<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('projects', function (Blueprint $table) {
			$table->id();
			$table->string('name');
			$table->longText('description')->nullable();
			$table->date('release_date')->nullable();
			$table->boolean('published')->nullable(false)->default(0);
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('projects');
	}
};
