<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('related_category', function (Blueprint $table) {
			$table->id();
			$table->foreignId('parent_id')->constrained('categories')->cascadeOnDelete();
			$table->foreignId('child_id')->constrained('categories')->cascadeOnDelete();
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('related_category');
	}
};
