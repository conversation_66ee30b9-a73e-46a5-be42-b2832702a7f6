<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('contacts', function (Blueprint $table) {
			$table->id();
			$table->string('name');
			$table->string('email');
			$table->string('phone')->nullable();
			$table->longtext('subject')->nullable();
			$table->longText('message');
			$table->softDeletes();
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('contacts');
	}
};
