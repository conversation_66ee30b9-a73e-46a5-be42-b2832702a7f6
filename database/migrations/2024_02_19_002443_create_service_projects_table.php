<?php

use App\Models\Project;
use App\Models\Service;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('service_project', function (Blueprint $table) {
			$table->id();
			$table->foreignIdFor(Service::class);
			$table->foreignIdFor(Project::class);
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('service_project');
	}
};
