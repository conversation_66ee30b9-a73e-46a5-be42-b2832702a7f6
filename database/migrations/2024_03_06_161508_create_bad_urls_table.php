<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	public function up(): void
	{
		Schema::create('bad_urls', function (Blueprint $table) {
			$table->id();
			$table->longText('url');
			$table->string('type')->nullable();
			$table->timestamps();
		});
	}
	
	
	public function down(): void
	{
		Schema::dropIfExists('bad_urls');
	}
};
