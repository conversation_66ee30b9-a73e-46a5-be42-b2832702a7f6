<?php

namespace Database\Factories;


use App\Models\Shortcut;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ShortcutFactory extends Factory
{
	protected $model = Shortcut::class;
	
	
	public function definition(): array
	{
		return [
			'original_url' => $this->faker->url(),
			'shortcut' => Str::slug($this->faker->sentence(5, true)),
			'created_at' => Carbon::now(),
			'updated_at' => Carbon::now(),
		];
	}
	
	
	public function configure(): static
	{
		return $this->afterCreating(function (Shortcut $shortcut) {
			for ($i = 0; $i < $this->faker->numberBetween(100, 1000); $i++) {
				$shortcut->views()->create(['visitor' => $this->faker->word, 'viewed_at' => $this->faker->dateTime]);
			}
		});
	}
}
