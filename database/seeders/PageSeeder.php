<?php

namespace Database\Seeders;


use App\Models\Page;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PageSeeder extends Seeder
{
	public function run(): void
	{
		$webFilePath = base_path('routes/web.php');
		$routeDetails = [];
		$routes = file($webFilePath);
		foreach ($routes as $route) {
			$route = trim($route);
			// Check if the line starts with Route::get() and contains a ->name()
			if (preg_match('/Route::get\(.*?,\s*(.*?)\s*\-\>/s', $route, $matches)) {
				$routeDefinition = $matches[1];
				preg_match('/->name\(\'(.+?)\'\)/', $route, $nameMatches);
				$routeName = count($nameMatches) > 1 ? $nameMatches[1] : null;
				$matches = [];
				preg_match('/\[([\w\\\\]+)::class,\s*[\'"](.+?)[\'"]\]/', $routeDefinition, $matches);
				$functionName = $matches[2];
				$controller = count($matches) > 2 ? $matches[1] : null;
				if (!empty($routeName) && !empty($controller)) {
					$view = $this->extractViewName($controller, $functionName);
					$routeDetails[] = [
						'method' => $functionName,
						'controller' => $controller,
						'view' => $view,
					];
				}
			}
		}
		foreach ($routeDetails as $route) {
			$routeName = ucwords(Str::replace('.', ' ', $route['method']));
			$controllerName = Str::replace('Controller', '', $route['controller']);
			if ($route['view']) {
				Page::updateOrCreate([
					'name' => $routeName . ' ' . $controllerName,
					'view' => $route['view'],
					'method' => $route['method'],
					'controller' => $route['controller'],
				]);
			}
		}
	}
	
	
	private function extractViewName($controllerClassName, $functionName): ?string
	{
		$controllerFilePath = app_path('Http/Controllers/' . $controllerClassName . '.php');
		if (!file_exists($controllerFilePath)) {
			return null;
		}
		$functionBody = $this->extractFunctionBody($controllerFilePath, $functionName);
		
		return $this->extractViewNameFromFunctionBody($functionBody);
	}
	
	
	private function extractFunctionBody($controllerFilePath, $functionName): ?string
	{
		if (!file_exists($controllerFilePath)) {
			return null;
		}
		$controllerContent = file_get_contents($controllerFilePath);
		$pattern = '/function\s+' . $functionName . '\s*\([^)]*\)\s*(\{(?:[^{}]+|(?1))*\})/s';
		preg_match($pattern, $controllerContent, $matches);
		
		return count($matches) > 1 ? $matches[1] : null;
	}
	
	
	private function extractViewNameFromFunctionBody($functionBody): ?string
	{
		$pattern = "/view\(\s*'([^']+)'/i";
		preg_match($pattern, $functionBody, $matches);
		
		return count($matches) > 1 ? $matches[1] : null;
	}
}
