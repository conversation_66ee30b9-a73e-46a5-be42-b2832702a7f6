<?php

namespace Database\Seeders;


use App\Models\Tool;
use Exception;
use Illuminate\Database\Seeder;

class ToolSeeder extends Seeder
{
	public function run(): void
	{
		$tools = [
			[
				'name' => 'Ollama',
				'icon' => 'https://ollama.com/public/ollama.png',
				'description' => 'Run large language models locally with ease—fast, efficient, and hassle-free.',
				'url' => 'https://ollama.com/',
			],
			[
				'name' => 'Aider',
				'icon' => 'https://avatars.githubusercontent.com/u/172139148',
				'description' => 'Aider lets you pair program with LLMs, to edit code in your local git repository.',
				'url' => 'https://aider.chat',
			],
			[
				'name' => 'Cline',
				'description' => 'Experience an AI development partner that amplifies your engineering capabilities.',
				'url' => 'https://cline.bot',
			],
			[
				'name' => 'Claude AI',
				'description' => 'AI that understands context. Generate, optimize, and create with ease.',
				'url' => 'https://claude.ai',
				'icon' => 'https://uxwing.com/wp-content/themes/uxwing/download/brands-and-social-media/claude-ai-icon.png',
			],
			[
				'name' => 'Gemini AI',
				'description' => 'Google’s AI powerhouse. Code smarter, write faster, automate everything.',
				'url' => 'https://gemini.google.com',
				'icon' => 'https://uxwing.com/wp-content/themes/uxwing/download/brands-and-social-media/google-gemini-icon.png',
			],
			[
				'name' => 'Unreal Engine',
				'description' => 'AAA game dev made effortless. Breathtaking visuals, real-time magic, infinite potential.',
				'url' => 'https://www.unrealengine.com',
				'icon' => 'https://cdn.worldvectorlogo.com/logos/unreal-1.svg',
			],
			[
				'name' => 'PHPStorm',
				'description' => 'The smartest PHP IDE. Code faster, debug smarter, and stay in the flow.',
				'url' => 'https://www.jetbrains.com/phpstorm/',
				'icon' => 'https://resources.jetbrains.com/storage/products/phpstorm/img/meta/phpstorm_logo_300x300.png',
			],
			[
				'name' => 'PyCharm',
				'description' => 'Python coding made seamless. AI-powered, lightning-fast, and built for productivity.',
				'url' => 'https://www.jetbrains.com/pycharm/',
				'icon' => 'https://resources.jetbrains.com/storage/products/pycharm/img/meta/pycharm_logo_300x300.png',
			],
			[
				'name' => 'VSCode',
				'description' => 'Fast, flexible, and feature-packed. The only editor you’ll ever need.',
				'url' => 'https://code.visualstudio.com',
				'icon' => 'https://code.visualstudio.com/assets/images/code-stable.png',
			],
			[
				'name' => 'Ubuntu',
				'description' => 'The developer’s OS. Stable, secure, and built for coding, cloud, and AI.',
				'url' => 'https://ubuntu.com',
				'icon' => 'https://assets.ubuntu.com/v1/29985a98-cof_orange_hex.png',
			],
			[
				'name' => 'Termius',
				'description' => 'SSH, but smarter. Sync devices, stay secure, and never retype a command again.',
				'url' => 'https://termius.com',
				'icon' => 'https://images.g2crowd.com/uploads/product/image/large_detail/large_detail_d86c8f1429f297e50b19410e40fe655c/termius.png',
			],
			[
				'name' => 'Postman',
				'description' => 'API testing redefined. Send requests, debug faster, automate workflows.',
				'url' => 'https://www.postman.com',
				'icon' => 'https://uxwing.com/wp-content/themes/uxwing/download/brands-and-social-media/postman-icon.png',
			],
			[
				'name' => 'Asset Links (DeepLink)',
				'description' => 'Instantly generate and test assetlinks.json. No hassle, just smooth deep linking.',
				'url' => route('tools.json.deeplink.create', [], false),
				'icon' => public_path('site/images/serv-img/4.png'),
			],
		];
		foreach ($tools as $tool) {
			$createdTool = Tool::updateOrCreate([
				'name' => $tool['name'],
			], [
				'description' => $tool['description'],
				'url' => $tool['url'],
			]);
			if (!empty($tool['icon'])) {
				try {
					$createdTool->clearMediaCollection(Tool::COLLECTION_ICON);
					$createdTool->addMediaFromUrl($tool['icon'])->toMediaCollection(Tool::COLLECTION_ICON);
				} catch (Exception) {
					//
				}
			}
		}
	}
}