<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

<meta name="title" content="Baraa Al Khateeb - Full Stack Developer">
<meta name="description"
      content="Baraa Al Khateeb Portfolio, showcasing skills in Full Stack Development, including Laravel and modern web technologies.">
<meta name="keywords" content="Baraa Al Khateeb, Full-Stack Developer, Baraa, Al Khateeb">
<meta name="author" content="Baraa Al Khateeb">
<meta name="designer" content="Baraa <PERSON> Khateeb">

<meta name="google-site-verification" content="Omot5nsdlUqr6wVs2vr0XFTcvV_rJKg5imp34e34KKM"/>

<link rel="canonical" href="{{ request()->url() }}">

<!-- Open Graph Meta Tags -->
<meta property="og:title" content="Baraa <PERSON>hateeb - Full Stack Developer"/>
<meta property="og:description"
      content="Explore the portfolio of <PERSON><PERSON> Khateeb, a Full Stack Developer specializing in Laravel and modern web technologies.">
<meta property="og:image" content="{{ asset('site/images/baraa.jpg') }}"/>
<meta property="og:url" content="{{ request()->url() }}"/>
<meta property="og:type" content="website"/>
<meta property="og:site_name" content="Baraa Al Khateeb Portfolio"/>

<!-- Twitter Meta Tags -->
<meta name="twitter:title" content="Baraa Al Khateeb - Full Stack Developer">
<meta name="twitter:description" content="Explore the portfolio of Baraa Al Khateeb, a Full Stack Developer.">
<meta name="twitter:image" content="{{ asset('site/images/baraa.jpg') }}">
<meta name="twitter:image:alt" content="Portrait image of Baraa Al Khateeb">
<meta name="twitter:card" content="summary_large_image">

<!-- Additional Meta Tags -->
<meta name="robots" content="index, follow">
<meta name="revisit-after" content="7 days">
<meta name="language" content="English">
<meta name="theme-color" content="#12ca93">
<meta name="msapplication-TileColor" content="#12ca93">
<meta name="msapplication-TileImage" content="{{ asset('favicons/ms-icon-144x144.png') }}">
<meta name="rating" content="General">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<meta name="format-detection" content="telephone=no">

<!-- Favicon -->
<link rel="icon" href="{{ asset('favicons/favicon.ico') }}" type="image/x-icon">
<link rel="shortcut icon" href="{{ asset('favicons/favicon.ico') }}" type="image/x-icon">

<!-- Apple Touch Icon -->
<link rel="apple-touch-icon" sizes="57x57" href="{{asset('favicons/apple-icon-76x76.png')}}">
<link rel="apple-touch-icon" sizes="60x60" href="{{asset('favicons/apple-icon-76x76.png')}}">
<link rel="apple-touch-icon" sizes="72x72" href="{{asset('favicons/apple-icon-76x76.png')}}">
<link rel="apple-touch-icon" sizes="76x76" href="{{asset('favicons/apple-icon-76x76.png')}}">

<link rel="apple-touch-icon" sizes="114x114" href="{{asset('favicons/apple-icon-180x180.png')}}">
<link rel="apple-touch-icon" sizes="120x120" href="{{asset('favicons/apple-icon-180x180.png')}}">
<link rel="apple-touch-icon" sizes="144x144" href="{{asset('favicons/apple-icon-180x180.png')}}">
<link rel="apple-touch-icon" sizes="152x152" href="{{asset('favicons/apple-icon-180x180.png')}}">
<link rel="apple-touch-icon" sizes="180x180" href="{{asset('favicons/apple-icon-180x180.png')}}">
<link rel="icon" type="image/png" sizes="192x192" href="{{asset('favicons/android-icon-192x192.png')}}">

<link rel="icon" type="image/png" sizes="32x32" href="{{asset('favicons/favicon-32x32.png')}}">
<link rel="icon" type="image/png" sizes="96x96" href="{{asset('favicons/favicon-32x32.png')}}">
<link rel="icon" type="image/png" sizes="16x16" href="{{asset('favicons/favicon-32x32.png')}}">
<link rel="manifest" href="{{asset('favicons/manifest.json')}}">
<!-- Microsoft Tile -->

<!-- Mobile Web App Capable -->
<meta name="mobile-web-app-capable" content="yes">

<!-- Preconnect and DNS-Prefetch -->
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="dns-prefetch" href="https://fonts.gstatic.com">


<!-- Manifest -->
<link rel="manifest" href="{{asset('favicons/manifest.json')}}?v={{config('app.asset_version')}}">

<!-- Schema.org Markup -->
<script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Baraa Al Khateeb",
      "jobTitle": "Software Engineer, Product Owner, and Full Stack Developer",
      "description": "Baraa Al Khateeb is a skilled software engineer, product owner, and full-stack developer specializing in modern web technologies, Unreal Engine development, and AI tools.",
      "url": "https://baraakhateeb.net/",
      "image": "https://baraakhateeb.net/site/images/baraa-al-khateeb.jpeg",
      "worksFor": {
        "@type": "Organization",
        "name": "Misraj AI Solutions",
        "url": "https://misraj.sa",
        "foundingDate": "2025",
        "location": {
          "@type": "Place",
          "address": "Riyadh, Saudi Arabia"
        }
      },
      "alumniOf": {
        "@type": "EducationalOrganization",
        "name": "Arab International University",
        "url": "https://www.aiu.edu.sy"
      },
      "sameAs": [
        "https://www.linkedin.com/in/baraa-khateeb"
      ],
      "contactPoint": {
          "@type": "ContactPoint",
          "contactType": "Personal",
          "email": "<EMAIL>",
          "areaServed": "Worldwide",
          "availableLanguage": ["English", "Arabic"]
        },
      "gender": "male",
      "knowsAbout": [
      "Full Stack Development",
      "Frontend Development",
      "Backend Development",
      "Web Development",
      "Mobile App Development",
      "Responsive Web Design",
      "Software Engineering",
      "System Design",
      "JavaScript (JS)",
      "TypeScript (TS)",
      "Python",
      "Node.js",
      "React",
      "Next.js",
      "Vue.js",
      "Unreal Engine",
      "Game Development",
      "3D Modeling",
      "3D Rendering",
      "Animation",
      "Virtual Reality (VR)",
      "Augmented Reality (AR)",
      "Extended Reality (XR)",
      "AI Tools",
      "Artificial Intelligence (AI)",
      "Machine Learning (ML)",
      "Deep Learning (DL)",
      "Natural Language Processing (NLP)",
      "Large Language Models (LLMs)",
      "OpenAI",
      "Retrieval-Augmented Generation (RAG)",
      "Chatbot Development",
      "AI Search Engines",
      "AI-Powered Search Systems",
      "Software Optimization",
      "Performance Tuning",
      "Scalable Systems",
      "System Architecture",
      "Cloud Computing",
      "AWS (Amazon Web Services)",
      "Google Cloud Platform (GCP)",
      "Microsoft Azure",
      "RESTful APIs",
      "GraphQL APIs",
      "Version Control",
      "Git and GitHub",
      "DevOps",
      "CI/CD Pipelines",
      "Containerization",
      "Docker",
      "Kubernetes",
      "Data Analysis",
      "Data Visualization",
      "Big Data",
      "Database Management",
      "SQL",
      "NoSQL",
      "Firebase",
      "MongoDB"
    ]
    }
</script>