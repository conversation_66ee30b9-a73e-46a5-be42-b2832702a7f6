@extends('website.layouts.master')
@section('seo')
    @if($category??false)
        {!! seo()->for($category) !!}
    @endif
@endsection
@section('content')
    <section class="portfolio section-padding">
        <div class="container">
            <div class="sec-head">
                <div class="row justify-content-center">
                    <div class="col-lg-8 text-center">
                        <div class="d-inline-block">
                            <div class="sub-title-icon d-flex align-items-center">
                                <span class="icon pe-7s-portfolio"></span>
                                <h6>My Portfolio</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="gallery">
                <div class="row">
                    @foreach($projects as $project)
                        <div class="col-lg-4 items">
                            <div class="item">
                                <div class="img">
                                    <img src="{{$project->cover}}" alt="">
                                    <a href="{{route('portfolio.project',['project'=>$project->id])}}" class="link"></a>
                                </div>
                                <div class="cont d-flex align-items-center">
                                    <div>
                                        <h6>{{$project->name}}</h6>
                                        @foreach($project->categories as $category)
                                            <a href="{{route('portfolio.index',['category'=>$category->id])}}"
                                               class="tag">{{$category->name}}</a>
                                        @endforeach
                                    </div>
                                    <div class="ml-auto">
                                        <div class="arrow">
                                            <a href="{{route('portfolio.project',['project'=>$project->id])}}">
                                                <svg class="arrow-right" xmlns="http://www.w3.org/2000/svg" x="0px"
                                                     y="0px" viewbox="0 0 34.2 32.3" xml:space="preserve"
                                                     style="stroke-width: 2;">
                                                    <line x1="0" y1="16" x2="33" y2="16"></line>
                                                    <line x1="17.3" y1="0.7" x2="33.2" y2="16.5"></line>
                                                    <line x1="17.3" y1="31.6" x2="33.5" y2="15.4"></line>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
@endsection