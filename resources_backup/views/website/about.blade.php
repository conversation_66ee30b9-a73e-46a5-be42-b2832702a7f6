@extends('website.layouts.master')

@section('content')
    <!-- ==================== Start About ==================== -->

    <section class="about section-padding">
        <div class="container with-pad">
            <div class="row lg-marg">
                <div class="col-lg-5 valign">
                    <div class="profile-img">
                        <div class="img">
                            <img src="{{asset('site/images/baraa.jpg')}}" alt="">
                        </div>
                        <span class="icon">
                                <img src="{{asset('site/images/header/icon1.png')}}" alt="">
                            </span>
                        <span class="icon">
                                <img src="{{asset('site/images/header/icon2.png')}}" alt="">
                            </span>
                        <span class="icon">
                                <img src="{{asset('site/images/header/icon3.png')}}" alt="">
                            </span>
                        <span class="icon">
                                <img src="{{asset('site/images/header/icon4.png')}}" alt="">
                            </span>
                    </div>
                </div>
                <div class="col-lg-7 valign">
                    <div class="cont">
                        <div class="sub-title-icon d-flex align-items-center">
                            <span class="icon pe-7s-gleam"></span>
                            <h6>About Me</h6>
                        </div>
                        <div class="text">
                            <h4 class="mb-30">I'm <span class="main-color">Full-Stack Developer & UI-UX Designer</span>
                                doing system analysis and design.</h4>
                            <p>I transform complex challenges into elegant, efficient, and user-centric web
                                applications. My expertise spans front-end and back-end technologies, ensuring seamless
                                integration and optimal performance. I have a proven track record of delivering
                                innovative software solutions for a wide range of clients.</p>

                            <div class="feat mt-30">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="item mb-15">
                                            <h6 class="sub-title ls1">
                                                <span class="fz-13 mr-10 main-color">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                                Software Analysis
                                            </h6>
                                        </div>
                                        <div class="item sm-mb15">
                                            <h6 class="sub-title mb-15">
                                                <span class="fz-13 mr-10 main-color">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                                Prototyping
                                            </h6>
                                        </div>
                                        <div class="item sm-mb15">
                                            <h6 class="sub-title ls1">
                                                <span class="fz-13 mr-10 main-color">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                                Implementation
                                            </h6>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="item mb-15">
                                            <h6 class="sub-title ls1">
                                                <span class="fz-13 mr-10 main-color">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                                System Design
                                            </h6>
                                        </div>
                                        <div class="item">
                                            <h6 class="sub-title mb-15">
                                                <span class="fz-13 mr-10 main-color">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                                Database Design
                                            </h6>
                                        </div>
                                        <div class="item">
                                            <h6 class="sub-title ls1">
                                                <span class="fz-13 mr-10 main-color">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                                Automated Testing
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="info mt-50">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <a href="mailto:<EMAIL>"
                                           class="item d-flex align-items-center sm-mb30">
                                            <div class="mr-15">
                                                <span class="icon pe-7s-mail"></span>
                                            </div>
                                            <div>
                                                <span class="opacity-7 mb-5">Email Me</span>
                                                <h6><EMAIL></h6>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ==================== End About ==================== -->


    @if(count($skills))
        <!-- ==================== Start Skills ==================== -->

        <section class="skills section-padding pt-0">
            <div class="container with-pad">
                <div class="sec-head mb-80">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="sub-title-icon d-flex align-items-center">
                                <span class="icon pe-7s-gym"></span>
                                <h6>My Skills</h6>
                            </div>
                            <h3>What i Do</h3>
                        </div>
                    </div>
                </div>
                <div class="row">
                    @foreach($skills as $skill)
                        <div class="col-lg-4 col-md-6">
                            <div class="item mb-30">
                                <div class="d-flex align-items-center mb-30">
                                    <div class="mr-30">
                                        <div class="img icon-img-40">
                                            <img src="{{$skill->logo}}" alt="{{$skill->name}} icon">
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="fz-18">{{$skill->name}}</h6>
                                    </div>
                                </div>
                                <div class="skill-progress">
                                    <span class="progress" data-value="{{$skill->percent}}%"></span>
                                </div>
                                <span class="value">{{$skill->percent}}%</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>

        <!-- ==================== End Skills ==================== -->
    @endif

    @if(count($experiences))
        <!-- ==================== Start Resume ==================== -->

        <section class="resume section-padding pt-0">
            <div class="container with-pad">
                <div class="sec-head mb-80">
                    <div class="row justify-content-center">
                        <div class="col-lg-6 text-center">
                            <div class="d-inline-block">
                                <div class="sub-title-icon d-flex align-items-center">
                                    <span class="icon pe-7s-note2"></span>
                                    <h6>My Journey</h6>
                                </div>
                            </div>
                            <h3>Experience</h3>
                        </div>
                    </div>
                </div>
                <div class="resume-swiper owl-carousel owl-theme">
                    @foreach($experiences ?? [] as $experience)
                        <a @if($experience->url) href="{{ $experience->url }}" @endif class="item text-center d-flex flex-column align-items-center text-decoration-none">
                            <img src="{{ $experience->logo }}" alt="{{ $experience->company }}" class="mb-20 mx-auto logo">
                            <h6 class="main-color date fz-15 mb-60">{{ $experience->start->format('M-Y') }} | {{ $experience->finish?->format('M-Y') ?? "Present" }}</h6>
                            <h5>{{ $experience->name }}</h5>
                            <span class="opacity-8 fw-500 mt-10">[ at {{ $experience->company }} ]</span>
                            @if($experience->description)
                                <p class="fz-13 mt-15">{{ $experience->description }}</p>
                            @endif
                        </a>
                    @endforeach
                </div>
            </div>
        </section>

        <!-- ==================== End Resume ==================== -->
    @endif
@endsection

@pushonce('scripts')
@endpushonce