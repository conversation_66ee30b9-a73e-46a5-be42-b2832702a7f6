<?php

use App\Http\Controllers\ContactController;
use App\Http\Controllers\DeepLinkController;
use App\Http\Controllers\ToolsController;
use App\Http\Controllers\WebsiteController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('app');
})->name('index');
//Route::get('/pricing', [WebsiteController::class, 'pricing'])->name('pricing');
Route::get('/cv', [WebsiteController::class, 'cv'])->name('cv');
Route::group(['prefix' => 'projects', 'as' => 'projects.'], function () {
	Route::get('/{category?}', [WebsiteController::class, 'projects'])->name('index');
	Route::get('/see/{project}', [WebsiteController::class, 'project'])->name('project');
});
Route::get('/services', [WebsiteController::class, 'services'])->name('services');
Route::get('/about', [WebsiteController::class, 'about'])->name('about');
Route::group(['prefix' => 'contact', 'as' => 'contact'], function () {
	Route::post('/', [ContactController::class, 'submitContact'])->name('submit');
	Route::get('/', [ContactController::class, 'index']);
});
//Route::get('url/{shortcut}', [ShortcutController::class, 'handle'])->name('handle');
Route::group(['prefix' => 'tools', 'as' => 'tools.'], function () {
	Route::get('/', [ToolsController::class, 'index'])->name('index');
	Route::group(['prefix' => 'json', 'as' => 'json.'], function () {
		Route::group(['prefix' => 'deeplink', 'as' => 'deeplink.'], function () {
			Route::get('create', [ToolsController::class, 'deeplink'])->name('create');
			Route::post('test', [DeepLinkController::class, 'test'])->name('test');
		});
	});
	Route::get('resolve', [ToolsController::class, 'resolve'])->name('resolve');
//	Route::group(['prefix' => 'url', 'as' => 'url.'], function () {
//		Route::get('create', [ToolsController::class, 'urls'])->name('create');
//		Route::post('store', [ShortcutController::class, 'store'])->name('store');
//	});
});

// React SPA catch-all route for client-side routing (should be last)
// Exclude API routes, admin routes, storage, and existing Laravel routes
Route::get('/{any}', function () {
    return view('app');
})->where('any', '^(?!api|admin|storage|cv|projects|services|about|contact|tools).*$')->name('react.spa');