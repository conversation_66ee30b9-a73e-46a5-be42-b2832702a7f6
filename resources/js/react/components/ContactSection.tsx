
import { useState } from 'react';
import { Mail, Github, Linkedin, Send } from 'lucide-react';
import GlassCard from './GlassCard';

const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const socialLinks = [
    { icon: Github, href: "#", label: "GitHub" },
    { icon: Linkedin, href: "#", label: "LinkedIn" },
    { icon: Mail, href: "#", label: "Email" }
  ];

  return (
    <section className="pb-32 px-6 bg-gradient-to-b from-slate-900 to-slate-950">
      <div className="max-w-5xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="w-16 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent mx-auto mb-8" />
          <span className="text-sm font-light text-white/60 tracking-[0.3em] uppercase mb-8 block">Contact</span>
          <h3 className="text-4xl md:text-6xl font-extralight text-white mb-6">
            Let's Connect
          </h3>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto font-light">
            Ready to discuss your next project? I'm here to help bring your vision to life
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Contact Form */}
          <GlassCard className="border-white/5">
            <form onSubmit={handleSubmit} className="space-y-6 relative z-10">
              <div>
                <label htmlFor="name" className="block text-sm font-light text-white/70 mb-3">
                  Full Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-white/20 focus:bg-white/10 transition-all duration-300 font-light relative z-20"
                  placeholder="Enter your name"
                  required
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-light text-white/70 mb-3">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-white/20 focus:bg-white/10 transition-all duration-300 font-light relative z-20"
                  placeholder="Enter your email"
                  required
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-light text-white/70 mb-3">
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows={6}
                  className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-white/20 focus:bg-white/10 transition-all duration-300 resize-none font-light relative z-20"
                  placeholder="Tell me about your project..."
                  required
                />
              </div>

              <button
                type="submit"
                className="w-full bg-white/10 border border-white/20 text-white font-light py-4 rounded-lg hover:bg-white/20 hover:border-white/30 transition-all duration-300 flex items-center justify-center gap-3 group backdrop-blur-xl relative z-20 cursor-pointer"
              >
                <span>Send Message</span>
                <Send className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
              </button>
            </form>
          </GlassCard>

          {/* Contact Info & Social Links */}
          <div className="space-y-8">
            {/* Contact Info */}
            <GlassCard className="border-white/5">
              <h4 className="text-xl font-light text-white mb-6">Get In Touch</h4>
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center">
                  <Mail className="w-5 h-5 text-white/70" />
                </div>
                <div>
                  <p className="text-white/60 text-sm font-light">Email</p>
                  <p className="text-white font-light"><EMAIL></p>
                </div>
              </div>
            </GlassCard>

            {/* Social Links */}
            <GlassCard className="border-white/5">
              <h4 className="text-xl font-light text-white mb-6">Connect</h4>
              <div className="space-y-4 relative z-10">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    className="flex items-center gap-4 p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-all duration-300 group border border-white/5 hover:border-white/10 relative z-20 cursor-pointer"
                  >
                    <social.icon className="w-5 h-5 text-white/60 group-hover:text-white transition-colors duration-300" />
                    <span className="text-white/70 group-hover:text-white transition-colors duration-300 font-light">
                      {social.label}
                    </span>
                  </a>
                ))}
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
