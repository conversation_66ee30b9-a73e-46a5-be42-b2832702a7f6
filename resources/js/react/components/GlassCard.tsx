
import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface GlassCardProps {
  children: ReactNode;
  className?: string;
  hover?: boolean;
  glow?: boolean;
}

const GlassCard = ({ children, className, hover = true, glow = false }: GlassCardProps) => {
  return (
    <div
      className={cn(
        "relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8",
        "shadow-[0_8px_32px_0_rgba(31,38,135,0.37)]",
        "before:absolute before:inset-0 before:rounded-3xl before:p-[1px] before:pointer-events-none",
        "before:bg-gradient-to-b before:from-white/20 before:to-transparent",
        "before:mask-[linear-gradient(white,white)_content-box,linear-gradient(white,white)]",
        "before:mask-composite-[exclude]",
        hover && "transition-all duration-500 hover:bg-white/10 hover:border-white/20 hover:shadow-[0_8px_32px_0_rgba(31,38,135,0.5)]",
        glow && "shadow-[0_0_40px_rgba(0,217,255,0.3)]",
        className
      )}
    >
      {children}
    </div>
  );
};

export default GlassCard;
