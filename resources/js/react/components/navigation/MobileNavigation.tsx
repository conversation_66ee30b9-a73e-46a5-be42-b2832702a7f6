import { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import MobileNavigationOverlay from './MobileNavigationOverlay';

interface NavItem {
  name: string;
  href: string;
}

interface MobileNavigationProps {
  activeSection: string;
  navItems: NavItem[];
}

const MobileNavigation = ({ activeSection, navItems }: MobileNavigationProps) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setIsScrolled(currentScrollY > 20);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu on resize to desktop (safety measure)
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768 && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobileMenuOpen]);

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  const handleMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuClose = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      {/* Mobile Navigation Bar */}
      <nav
        className={`
          fixed top-0 left-0 right-0 z-[9999] transition-all duration-300 px-4
          ${isScrolled ? 'py-2' : 'py-4'}
        `}
        style={{
          '--nav-height': isScrolled ? '64px' : '80px'
        } as React.CSSProperties}
      >
        <div className="max-w-7xl mx-auto">
          <div className={`
            transition-all duration-300 rounded-full border
            ${isScrolled
              ? 'bg-black/60 border-white/15 shadow-lg shadow-black/20'
              : 'bg-black/20 border-white/5'
            }
            backdrop-blur-xl px-4 py-3
            hover-glow
          `}>
            <div className="flex items-center justify-between">
              {/* Logo */}
              <a href="#" className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-white via-orange-100 to-orange-200 flex items-center justify-center">
                  <span className="text-black text-sm font-semibold">B</span>
                </div>
                <span className="text-white font-medium text-lg">
                  Baraa Al Khateeb
                </span>
              </a>

              {/* Mobile Menu Button */}
              <button
                className="text-white p-3 hover:bg-white/10 rounded-xl transition-all duration-150 relative z-10 min-h-[44px] min-w-[44px] flex items-center justify-center"
                onClick={handleMenuToggle}
                aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
              >
                {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation Overlay */}
      <MobileNavigationOverlay
        isOpen={isMobileMenuOpen}
        onClose={handleMenuClose}
        activeSection={activeSection}
        navItems={navItems}
      />
    </>
  );
};

export default MobileNavigation;
