
import { useEffect, useState } from 'react';
import { ArrowRight, Play } from 'lucide-react';
import ThreeJ<PERSON><PERSON>ground from './ThreeJSBackground';
import FloatingElements from './FloatingElements';

const HeroSection = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);

    // Only track mouse on desktop devices (768px+)
    const handleMouseMove = (e: MouseEvent) => {
      if (window.innerWidth >= 768) {
        setMousePosition({
          x: (e.clientX / window.innerWidth) * 100,
          y: (e.clientY / window.innerHeight) * 100
        });
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const scrollToAbout = () => {
    const aboutSection = document.getElementById('about');
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-black via-slate-950 to-black pt-20 sm:pt-24 lg:pt-28">
      {/* Three.js Background */}
      <ThreeJSBackground />
      
      {/* Floating Elements */}
      <FloatingElements />

      {/* Interactive Mouse Gradient with orange accent - Hidden on mobile */}
      <div
        className="absolute inset-0 opacity-10 transition-all duration-1000 hidden md:block"
        style={{
          background: `radial-gradient(800px circle at ${mousePosition.x}% ${mousePosition.y}%,
            rgba(249, 115, 22, 0.08),
            rgba(255, 255, 255, 0.05),
            transparent 40%)`
        }}
      />

      {/* Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div 
          className="absolute inset-0" 
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }} 
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-4 sm:px-6 max-w-7xl mx-auto">
        {/* Hero Badge */}
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="inline-flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 rounded-full bg-white/5 border border-white/10 backdrop-blur-xl mb-6 sm:mb-8 hover-glow">
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" />
            <span className="text-xs sm:text-sm text-white/80 font-medium">Available for new projects</span>
          </div>
        </div>

        {/* Main Headline */}
        <div className={`transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <h1 className="text-4xl sm:text-6xl lg:text-7xl xl:text-8xl font-light text-white mb-4 sm:mb-6 leading-[0.9] tracking-tight">
            One-click for
            <br />
            <span className="bg-gradient-to-r from-white via-orange-100 to-slate-200 bg-clip-text text-transparent font-extralight">
              Digital Excellence
            </span>
          </h1>
        </div>

        {/* Subtitle */}
        <div className={`transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <p className="text-lg sm:text-xl lg:text-2xl text-slate-400 max-w-4xl mx-auto leading-relaxed mb-8 sm:mb-12 font-light px-4">
            Dive into the art of software development, where innovative technology 
            meets exceptional digital experiences.
          </p>
        </div>

        {/* CTA Buttons */}
        <div className={`flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} px-4`}>
          <button className="group flex items-center gap-3 px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-white to-orange-50 text-black rounded-full font-medium hover:from-orange-50 hover:to-orange-100 transition-all duration-150 hover-lift hover-glow w-full sm:w-auto">
            <span>Open App</span>
            <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-150" />
          </button>
          
          <button className="group flex items-center gap-3 px-6 sm:px-8 py-3 sm:py-4 bg-white/5 border border-white/10 rounded-full text-white font-medium hover:bg-white/10 hover:border-orange-500/20 transition-all duration-150 backdrop-blur-xl hover-lift w-full sm:w-auto">
            <Play className="w-4 h-4" />
            <span>Discover More</span>
          </button>
        </div>

        {/* Stats Row */}
        <div className={`grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-8 mt-12 sm:mt-20 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} px-4`}>
          {[
            { label: "Cortex", value: "70,045", icon: "△" },
            { label: "Projects", value: "50+", icon: "◇" },
            { label: "Clients", value: "100+", icon: "○" },
            { label: "Success Rate", value: "100%", icon: "◆" }
          ].map((stat, index) => (
            <div key={index} className="text-center group hover-lift">
              <div className="text-white/40 text-xl sm:text-2xl mb-2 group-hover:text-orange-400/60 transition-colors duration-150">
                {stat.icon}
              </div>
              <div className="text-xl sm:text-2xl font-light text-white mb-1">{stat.value}</div>
              <div className="text-xs sm:text-sm text-slate-500 font-medium">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Enhanced Scroll Indicator */}
        <div
          onClick={scrollToAbout}
          className={`absolute bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 transition-all duration-1000 delay-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} hidden sm:block group cursor-pointer`}
        >
          <div className="flex flex-col items-center gap-3 hover-lift">
            <div className="text-xs text-slate-400 font-medium tracking-wider uppercase group-hover:text-orange-400/80 transition-colors duration-300">
              Scroll down
            </div>
            <div className="relative flex justify-center items-start">
              <div className="w-0.5 h-12 bg-gradient-to-b from-orange-400/40 via-white/30 to-transparent group-hover:from-orange-400/60 transition-colors duration-300" />
              <div className="w-2 h-2 bg-orange-400/70 rounded-full absolute top-0 left-1/2 -translate-x-1/2 animate-bounce group-hover:bg-orange-400 transition-colors duration-300" />
              <div className="w-1 h-1 bg-white/40 rounded-full absolute top-4 left-1/2 -translate-x-1/2 animate-pulse" />
            </div>
            <div className="text-[10px] text-slate-600 font-light opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              Explore my work
            </div>
          </div>
        </div>
      </div>

      {/* Ambient Effects with orange accents */}
      <div className="absolute top-1/4 left-1/4 w-64 sm:w-96 h-64 sm:h-96 bg-gradient-to-r from-orange-500/[0.02] to-white/[0.01] rounded-full filter blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-60 sm:w-80 h-60 sm:h-80 bg-slate-400/[0.03] rounded-full filter blur-3xl" />
    </section>
  );
};

export default HeroSection;
