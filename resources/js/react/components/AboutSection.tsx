
import { useEffect, useRef, useState } from 'react';
import Glass<PERSON>ard from './GlassCard';

const AboutSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const expertise = [
    { 
      number: "01", 
      title: "Frontend Architecture", 
      description: "Modern React ecosystems with TypeScript, Next.js, and advanced state management patterns."
    },
    { 
      number: "02", 
      title: "Backend Systems", 
      description: "Scalable APIs, microservices, and cloud infrastructure using Node.js, Python, and modern DevOps."
    },
    { 
      number: "03", 
      title: "AI Integration", 
      description: "Machine learning models, neural networks, and intelligent automation systems."
    }
  ];

  return (
    <section ref={sectionRef} className="py-32 px-6 bg-gradient-to-b from-slate-950 to-slate-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, white 1px, transparent 1px),
                           radial-gradient(circle at 75% 75%, white 1px, transparent 1px)`,
          backgroundSize: '100px 100px'
        }} />
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="max-w-4xl mb-24">
          <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-px bg-gradient-to-r from-white/30 to-transparent" />
              <span className="text-sm font-light text-white/60 tracking-[0.2em] uppercase">About</span>
            </div>
            <h2 className="text-4xl md:text-6xl font-extralight text-white mb-8 leading-tight">
              Engineering the future,
              <br />
              <span className="text-slate-400">one solution at a time</span>
            </h2>
            <p className="text-xl text-slate-400 leading-relaxed font-light max-w-3xl">
              I'm Baraa Al Khateeb, a software engineer passionate about creating sophisticated digital experiences 
              that bridge the gap between cutting-edge technology and human-centered design.
            </p>
          </div>
        </div>

        {/* Expertise Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-24">
          {expertise.map((item, index) => (
            <div
              key={index}
              className={`transform transition-all duration-700 ${
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'
              }`}
              style={{ transitionDelay: `${index * 200}ms` }}
            >
              <GlassCard className="h-full group cursor-pointer border-white/5 hover:border-white/10 p-8">
                <div className="flex items-start gap-6">
                  <div className="text-5xl font-extralight text-white/20 group-hover:text-white/30 transition-colors duration-500 mt-2">
                    {item.number}
                  </div>
                  <div>
                    <h3 className="text-xl font-light text-white mb-4 group-hover:text-white/90 transition-colors duration-300">
                      {item.title}
                    </h3>
                    <p className="text-slate-400 leading-relaxed font-light text-sm">
                      {item.description}
                    </p>
                  </div>
                </div>
              </GlassCard>
            </div>
          ))}
        </div>

        {/* Philosophy Statement */}
        <div className={`transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <GlassCard className="text-center max-w-5xl mx-auto border-white/5 p-12">
            <blockquote className="text-2xl md:text-4xl font-extralight text-white/90 leading-relaxed mb-8">
              "The intersection of technology and creativity 
              <br />
              <span className="text-slate-400">is where true innovation emerges."</span>
            </blockquote>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mx-auto mb-8" />
            <p className="text-slate-400 text-lg leading-relaxed font-light max-w-3xl mx-auto">
              My approach combines deep technical expertise with strategic thinking, 
              ensuring every solution is not just functional, but transformative.
            </p>
          </GlassCard>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
