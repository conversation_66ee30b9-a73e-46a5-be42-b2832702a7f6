@extends('website.layouts.master')
@section('seo')
    {!! seo()->for($project) !!}
@endsection
@section('content')
    <!-- ==================== Start project ==================== -->

    <section class="project section-padding radius-15">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12">
                    <div class="img mb-80">
                        <img src="{{$project->cover}}"
                             alt="{{$project->name}} project cover image" class="radius-5" style="max-height: 75vh">
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-lg-7">
                            <div class="cont md-mb50">
                                <x-markdown>
                                    {!! $project->description !!}
                                </x-markdown>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div id="sticky_item">
                                <div class="info">
                                    <ul>
                                        @if($project->release_date)
                                            <li class="mb-30">
                                                <span class="sub-title"><i class="far fa-calendar-alt mr-10"></i>
                                                    Date:</span>
                                                <p>{{$project->release_date->format('d-M-Y')}}</p>
                                            </li>
                                        @endif
                                        @if($project->categories_count)
                                            <li class="mb-30">
                                                <span class="sub-title"><i class="fas fa-list-ul mr-10"></i> Categories :</span>
                                                <ul>
                                                    @foreach($project->categories as $category)
                                                        <li>
                                                            <a href="{{route('portfolio.index',['category'=>$category->id])}}">{{$category->name}}</a>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </li>
                                        @endif
                                        @if($project->original_url)
                                            <li>
                                                <span class="sub-title">
                                                    <i class="fas fa-globe mr-10"></i> Live Url :
                                                </span>
                                                <br>
                                                <a target="_blank"
                                                   href="{{$project->original_url}}">{{$project->original_url}}</a>
                                            </li>
                                        @endif
                                        @if($project->demo_url)
                                            <li class="mt-3">
                                                <span class="sub-title">
                                                    <i class="fas fa-globe mr-10"></i>
                                                    Demo Url :
                                                </span>
                                                <br>
                                                <a target="_blank"
                                                   href="{{$project->demo_url}}">{{$project->demo_url}}</a>
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="next-prev">
                <div class="row justify-content-center">
                    <div class="col-lg-12">
                        <div class="d-flex align-items-center mt-80 pt-80 bord-thin-top">
                            <div class="prev">
                                <h6 class="sub-title">
                                    <a href="{{ $project->prev ? route('portfolio.project', $project->prev) : route('portfolio.index') }}">
                                        <i class="fas fa-long-arrow-alt-left"></i>
                                        {{ $project->prev ? 'Previous Project' : 'All Projects' }}
                                    </a>

                                </h6>
                            </div>
                            <div class="next ml-auto">
                                <h6 class="sub-title">
                                    <a href="{{ $project->next ? route('portfolio.project', $project->next) : route('portfolio.index') }}">
                                        {{ $project->next ? 'Next Project' : 'All Projects' }}
                                        <i class="fas fa-long-arrow-alt-right"></i>
                                    </a>
                                </h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ==================== End project ==================== -->
@endsection