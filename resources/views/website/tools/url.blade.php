@extends('website.layouts.master')
@pushonce('styles')
    <style>
        .error_message {
            color: #DD0000;
            border: #DD0000 1px solid;
            border-radius: 15px;
        }

        .swal2-confirm {
            background-color: var(--maincolor) !important;
        }
    </style>
@endpushonce
@section('content')
    <main class="pt-80">

        <!-- ==================== Start Contact ==================== -->

        <section class="contact">
            <div class="container with-pad">
                <div class="sec-head mb-80">
                    <div class="row justify-content-center">
                        <div class="col-lg-8 text-center">
                            <div class="d-inline-block">
                                <div class="sub-title-icon d-flex align-items-center">
                                    <span class="icon pe-7s-bell"></span>
                                    <h6>Free To Use</h6>
                                </div>
                            </div>
                            <h3>Shorten Your Link</h3>
                        </div>
                    </div>
                </div>
                <div class="valign">
                    <div class="full-width">
                        <form id="form" method="post" action="{{route('tools.url.store')}}">
                            @csrf
                            <div class="controls row">
                                <div class="col-lg-12">
                                    <div class="form-group mb-30">
                                        <label>Your Link</label>
                                        <input type="url" name="original_url" required="required"
                                               class="original_url">
                                    </div>
                                    <div class="form-group mb-30">
                                        <label>Shortcut | Optional</label>
                                        <input minlength="3" maxlength="15" id="shortcut" type="text"
                                               name="shortcut" required="required">
                                    </div>
                                    <button type="submit" id="submitButton">
                                        <span class="text">Save</span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            </div>
        </section>

        <!-- ==================== End Contact ==================== -->


    </main>
@endsection
@push('scripts')
    <script src="{{asset('site/js/shortener.js')}}"></script>
@endpush