@extends('website.layouts.master')
@pushonce('styles')
    <style>
        .error_message {
            color: #DD0000;
            border: #DD0000 1px solid;
            border-radius: 15px;
        }

        .swal2-confirm {
            background-color: var(--maincolor) !important;
        }
    </style>
@endpushonce
@section('content')
    <main class="pt-80">

        <!-- ==================== Start Contact ==================== -->

        <section class="contact">
            <div class="container with-pad">
                <div class="sec-head mb-80">
                    <div class="row justify-content-center">
                        <div class="col-lg-8 text-center">
                            <div class="d-inline-block">
                                <div class="sub-title-icon d-flex align-items-center">
                                    <span class="icon pe-7s-bell"></span>
                                    <h6>Free To Use</h6>
                                </div>
                            </div>
                            <h3>Asset Links (DeepLinking)</h3>
                        </div>
                    </div>
                </div>
                <div class="valign">
                    <div class="full-width">
                        <form id="form" method="post" action="{{route('tools.json.deeplink.test')}}">
                            @csrf
                            <div class="controls row">
                                <div class="col-lg-12">
                                    <div class="form-group mb-30">
                                        <label>Hosting site domain</label>
                                        <input name="domain" id="domain" required="required">
                                    </div>
                                    <div class="form-group mb-30">
                                        <label>App package name</label>
                                        <input id="package" type="text" name="package" required="required">
                                    </div>
                                    <div class="form-group mb-30">
                                        <label>App package fingerprint (SHA256)</label>
                                        <input id="sha" type="text" name="sha" required="required">
                                    </div>
                                    <div class="d-flex">
                                        <button type="submit" id="testButton">
                                            <span class="text">Test</span>
                                        </button>
                                        <button type="button" id="generateButton">
                                            <span class="text">Generate</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- ==================== End Contact ==================== -->


    </main>
@endsection
@push('scripts')
    <script src="{{asset('site/js/deep.js')}}"></script>
@endpush