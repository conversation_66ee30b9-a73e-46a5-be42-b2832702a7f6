@extends('website.layouts.master')

@section('content')
    <!-- ==================== Start main box ==================== -->

    <section class="box">
        <div class="container">
            <div class="row md-marg">
                <div class="col-lg-4">
                    <div class="author-profile pt-80 pb-80" id="sticky_item">
                        <div class="cont">
                            <div class="img">
                                <img src="{{asset('site/images/baraa-portrait.jpg')}}"
                                     alt="portrait photo of Baraa Al Khateeb">
                            </div>
                            <div class="info text-center mt-30">
                                <h5>Baraa Al Khateeb</h5>
                                <p><a href="https://linkedin.com/in/baraa-khateeb/" aria-label="Baraa al khateeb linkedin profile">@Baraa_khb</a></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-7 offset-lg-1">

                    <!-- ==================== Start about ==================== -->

                    <div class="about section-padding" id="bio" data-scroll-index="0">
                        <div class="cont">
                            <h1>Hello, I’m <span class="main-color">Baraa Al Khateeb</span>, <span
                                        class="bord">Full-Stack</span> Developer<br>With a UX/UI Passion.<i></i></h1>
                        </div>
                        <div class="status mt-80">
                            <div class="d-flex align-items-center">
                                <div class="mr-40">
                                    <div class="d-flex align-items-center">
                                        <h2>{{$experienceYears}}</h2>
                                        <p>Years <br> of Experience</p>
                                    </div>
                                </div>
                                <div class="mr-40">
                                    <div class="d-flex align-items-center">
                                        <h2>{{$clientsCount}}</h2>
                                        <p>Clients <br> Worldwide</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text mt-80">
                            <h6 class="sub-title mb-15">About Me.</h6>
                            <p class="fz-20">Hello, I'm Baraa Al Khateeb, a passionate full-stack developer with a
                                software
                                engineering degree from Arab International University. I excel in UI/UX design and web
                                development, combining creativity and functionality seamlessly.
                                <br>
                                My philosophy: Strive for excellence in every line of code and every pixel of design.
                                I'm always eager for new challenges and opportunities.</p>
                        </div>
                    </div>

                    <!-- ==================== End about ==================== -->


                    <!-- ==================== Start Skills ==================== -->

                    <div class="skills section-padding pt-0" id="skills" data-scroll-index="1">
                        <div class="sec-head bord-thin-bottom pb-20 mb-80">
                            <h4 class="sub-title fz-28">Skills</h4>
                        </div>
                        <div class="row">
                            @foreach($skills??[] as $skill)
                                <div class="col-md-6">
                                    <div class="item mb-30">
                                        <div class="d-flex align-items-center mb-30">
                                            <div class="mr-30">
                                                <div class="img icon-img-40">
                                                    <img class="rounded-3" src="{{$skill->logo}}"
                                                         alt="{{$skill->name}} icon">
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="fz-18">{{$skill->name}}</h6>
                                            </div>
                                        </div>
                                        <div class="skill-progress">
                                            <span class="progress" data-value="90%"></span>
                                        </div>
                                        <span class="value">{{$skill->percent}}%</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- ==================== End Skills ==================== -->

                </div>
            </div>
        </div>
    </section>

    <!-- ==================== End main box ==================== -->


    <!-- ==================== Start Services ==================== -->

    <section class="services section-padding pt-40" id="services" data-scroll-index="2">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="sec-head bord-thin-bottom pb-20 mb-80">
                        <h4 class="sub-title fz-28">Services</h4>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="item md-mb30">
                        <h3 class="letter">E</h3>
                        <h6>E-Solutions</h6>
                        <p>Software solutions to streamline your business operations and enhance
                            your productivity.</p>
                        <div class="tags">
                            <a href="#0">Laravel</a>
                            <a href="#0">Python</a>
                            <a href="#0">NodeJs</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="item md-mb30">
                        <h3 class="letter">W</h3>
                        <h6>Web Design</h6>
                        <p>Modern and mobile-responsive website that will help you reach all of your customers.</p>
                        <div class="tags">
                            <a href="#0">UI / UX</a>
                            <a href="#0">CMS</a>
                            <a href="#0">React</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="item sm-mb30">
                        <h3 class="letter">M</h3>
                        <h6>Mobile Apps</h6>
                        <p>Customized applications that bring your ideas to life, delivering enhanced functionality.</p>
                        <div class="tags">
                            <a href="#0">UI / UX</a>
                            <a href="#0">Flutter</a>
                            <a href="#0">Native</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="item">
                        <h3 class="letter">3</h3>
                        <h6>3D Apps</h6>
                        <p>Immersive and interactive 3D applications that push the boundaries of visual experiences.</p>
                        <div class="tags">
                            <a href="#0">UE5</a>
                            <a href="#0">ThreeJs</a>
                            <a href="#0">WebGL</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ==================== End Services ==================== -->
    {{--        @include('website.partials.sections.portfolio')--}}

    {{--    @include('website.partials.sections.testimonials')--}}


    {{--            @include('website.partials.sections.news')--}}


    <!-- ==================== Start Contact ==================== -->

    <section class="contact section-padding mt-80 bord-thin-top" id="contact" data-scroll-index="6">
        <div class="container">
            <div class="row">
                <div class="col-lg-5">
                    <div class="sec-head md-mb80">
                        <h6 class="dot-title mb-10">Get In Touch</h6>
                        <h2 class="fz-50">Let's bring your idea to life</h2>
                        <p class="fz-15 mt-10">If you'd like to work with us or just get in touch, we're here and
                            looking forward to hearing from you !</p>
                        <ul class="rest social-text d-flex mt-60">
                            @if(!empty($facebook_url))
                                <li class="mr-30">
                                    <a href="{{$facebook_url}}" class="hover-this">
                                        <span class="hover-anim">Facebook</span>
                                    </a>
                                </li>
                            @endif
                            @if(!empty($linkedin_url))
                                <li class="mr-30">
                                    <a href="{{$linkedin_url}}" class="hover-this">
                                        <span class="hover-anim">LinkedIn</span>
                                    </a>
                                </li>
                            @endif
                            @if(!empty($pinterest_url))
                                <li class="mr-30">
                                    <a href="{{$pinterest_url}}" class="hover-this">
                                        <span class="hover-anim">Pinterest</span>
                                    </a>
                                </li>
                            @endif
                            @if(!empty($instagram_url))
                                <li class="mr-30">
                                    <a href="{{$instagram_url}}" class="hover-this">
                                        <span class="hover-anim">Instagram</span>
                                    </a>
                                </li>
                            @endif
                            @if(!empty($twitter_url))
                                <li class="mr-30">
                                    <a href="{{$twitter_url}}" class="hover-this">
                                        <span class="hover-anim">Twitter</span>
                                    </a>
                                </li>
                            @endif
                            @if(!empty($youtube_url))
                                <li class="mr-30">
                                    <a href="{{$youtube_url}}" class="hover-this">
                                        <span class="hover-anim">Youtube</span>
                                    </a>
                                </li>
                            @endif
                            @if(!empty($whatsapp_url))
                                <li class="mr-30">
                                    <a href="{{$whatsapp_url}}" class="hover-this">
                                        <span class="hover-anim">Whatsapp</span>
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </div>
                </div>
                <div class="col-lg-6 offset-lg-1 valign">
                    <div class="full-width">
                        <form id="contactForm" method="post" action="{{ route('contact') }}">
                            @csrf
                            <div class="controls row">
                                <div class="col-lg-6">
                                    <div class="form-group mb-30">
                                        <label class="form-label" for="form_name">Full Name *</label>
                                        <input id="form_name" type="text" name="name" placeholder="Name"
                                               required="required">
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group mb-30">
                                        <label class="form-label" for="form_email">Email *</label>
                                        <input id="form_email" type="email" name="email" placeholder="Email"
                                               required="required">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group mb-30">
                                        <label class="form-label" for="form_subject">Subject</label>
                                        <input id="form_subject" type="text" name="subject" placeholder="Subject">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label" for="form_message">Message *</label>
                                        <textarea class="emerald-scroll-bar" id="form_message" name="message"
                                                  placeholder="Message" rows="4"
                                                  required="required"></textarea>
                                    </div>
                                    <div class="mt-30">
                                        <button type="button" id="submitButton">
                                            <span class="text">Send A Message</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ==================== End Contact ==================== -->
@endsection

@pushonce('scripts')
    <script>
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });
    </script>
@endpushonce