$(document).ready(function () {
    const inputElement = $(".original_url");
    const inputLabel = inputElement.siblings("label");

    $('input[type="text"]').keydown(function (event) {
        if (event.keyCode === 32) {
            event.preventDefault();
        }
    });

    inputElement.on("input", function () {
        const inputValue = inputElement.val();
        const isValidUrl = /^(https?|ftp):\/\/([^\s/$.?#].[^\s]*)$/i.test(inputValue);

        inputLabel.text(isValidUrl ? 'Your Link' : 'Invalid Url');
        inputLabel.toggleClass("error_message", !isValidUrl);
    });

    $('#form').submit(function (e) {
        e.preventDefault(); // Prevent the form from submitting normally

        let form = $(this);
        let url = form.attr('action');

        $.ajax({
            type: 'POST',
            url: url,
            data: form.serialize(), // Serialize the form data
            success: function (response) {
                let url = response.json.url;
                Swal.fire({
                    title: "Success",
                    text: response.message,
                    icon: "success",
                    confirmButtonText: "Share",
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showCloseButton: false,
                    showCancelButton: false,
                    preConfirm: () => {
                        if (navigator.share) {
                            navigator.clipboard.writeText(url);
                            return navigator.share({
                                title: "Share Title",
                                text: 'See my link 👀 , ' + url,
                                url: url
                            }).then(() => {
                                Swal.fire("Shared", "Shared successfully.", "success");
                            }).catch((error) => {
                                Swal.fire("Error", "Failed to share your link.", "error");
                            });
                        } else {
                            Swal.fire("Error", "Sharing is not supported in this browser.", "error");
                        }
                    }
                });
            },
            error: function (xhr, status, error) {
                let errorResponse = xhr.responseJSON;
                let errorMessage = errorResponse ? errorResponse.message : 'An error occurred while submitting the form.';
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: errorMessage,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showCloseButton: false,
                });
            }
        });
    });
});