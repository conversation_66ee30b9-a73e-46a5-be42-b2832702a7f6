$(document).ready(function () {
    const $contact = $('#contact-form');
    $contact.validator();

    $contact.on('submit', function (e) {
        if (!e.isDefaultPrevented()) {
            let url = "contact";
            $.ajax({
                type: "POST", url: url, data: $(this).serialize(), success: function (data) {
                    $contact.find('.error').remove();
                    let messageText = data.message;
                    if (messageText) {
                        Swal.fire({
                            title: messageText, icon: 'success', showConfirmButton: false, timer: 5000
                        });
                    }
                    $contact[0].reset();
                }, error: function (xhr, status, error) {
                    let errResponse = JSON.parse(xhr.responseText);
                    let errorMessages = errResponse.errors;
                    // Clear previous error messages
                    $contact.find('.error').remove();
                    if (errorMessages.message || errorMessages['g-recaptcha-response']) {
                        Swal.fire({
                            title: errorMessages['g-recaptcha-response'] ?? errorMessages.message,
                            icon: 'error',
                            showConfirmButton: false,
                            timer: 5000
                        });
                    }

                    // Loop through error messages and display them next to corresponding form fields
                    $.each(errorMessages, function (field, errors) {
                        let fieldElement = $('#form_' + field);
                        $.each(errors, function (index, errorMessage) {
                            let errorElement = $('<span class="error">' + errorMessage + '</span>');
                            fieldElement.after(errorElement);
                        });
                    });

                }
            });
            return false;
        }
    });
})