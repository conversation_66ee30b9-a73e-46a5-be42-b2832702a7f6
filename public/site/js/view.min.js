!function(){function e(e){e.querySelectorAll('[aria-expanded="true"]').forEach((function(e){e.setAttribute("aria-expanded","false")}))}function t(t){const n=t.target.closest("[aria-expanded]");if("true"===n.getAttribute("aria-expanded"))e(n.closest(".wp-block-navigation-item"));else{const t=n.closest(".wp-block-navigation-item");n.closest(".wp-block-navigation__submenu-container, .wp-block-navigation__container, .wp-block-page-list").querySelectorAll(".wp-block-navigation-item").forEach((function(n){n!==t&&e(n)})),n.setAttribute("aria-expanded","true")}}window.addEventListener("load",(()=>{document.querySelectorAll(".wp-block-navigation-submenu__toggle").forEach((function(e){e.addEventListener("click",t)})),document.addEventListener("click",(function(t){document.querySelectorAll(".wp-block-navigation").forEach((function(n){n.contains(t.target)||e(n)}))})),document.addEventListener("keyup",(function(t){document.querySelectorAll(".wp-block-navigation-item.has-child").forEach((function(n){if(n.contains(t.target)){if("Escape"===t.key){const t=n.querySelector('[aria-expanded="true"]');e(n),t?.focus()}}else e(n)}))}))}))}();