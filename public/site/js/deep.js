$(document).ready(function () {
    const domainInputElement = $("#domain");
    const domainInputLabel = domainInputElement.siblings("label");

    const packageInputElement = $("#package");
    const packageInputLabel = packageInputElement.siblings("label");

    $('input[type="text"]').keydown(function (event) {
        if (event.keyCode === 32) {
            event.preventDefault();
        }
    });

    domainInputElement.on("input", function () {
        const inputValue = domainInputElement.val();
        const isValidUrl = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/i.test(inputValue);

        domainInputLabel.text(isValidUrl ? 'Valid Domain' : 'Invalid Domain');
        domainInputLabel.toggleClass("error_message", !isValidUrl);
    });

    packageInputElement.on("input", function () {
        const inputValue = packageInputElement.val();
        const isValidPackage = /^[a-zA-Z][a-zA-Z\d_]*(\.[a-zA-Z][a-zA-Z\d_]*)+$/i.test(inputValue);

        packageInputLabel.text(isValidPackage ? 'Valid Package Name' : 'Invalid Package Name');
        packageInputLabel.toggleClass("error_message", !isValidPackage);
    });

    const shaInputElement = $("#sha");
    const shaInputLabel = shaInputElement.siblings("label");

    shaInputElement.on("input", function () {
        const inputValue = shaInputElement.val();
        const isValidSHA = /^([A-Fa-f0-9]{2}:){31}[A-Fa-f0-9]{2}$/.test(inputValue);

        shaInputLabel.text(isValidSHA ? 'Valid SHA256' : 'Invalid SHA256');
        shaInputLabel.toggleClass("error_message", !isValidSHA);
    });

    $('#form').submit(function (e) {
        e.preventDefault(); // Prevent the form from submitting normally

        let form = $(this);
        let url = form.attr('action');

        $.ajax({
            type: 'POST', url: url, data: form.serialize(), // Serialize the form data
            beforeSend: function () {
                Swal.fire({
                    title: 'Loading',
                    text: 'Please wait...',
                    icon: 'info',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showCloseButton: false,
                    showCancelButton: false,
                    showConfirmButton: false,
                    onOpen: () => {
                        Swal.showLoading();
                    }
                });
            }, success: function (response) {
                Swal.fire({
                    title: 'Success',
                    text: response.message,
                    icon: 'success',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showCloseButton: false,
                    showCancelButton: false,
                });
            }, error: function (xhr, status, error) {
                let errorResponse = xhr.responseJSON;
                let errorMessage = errorResponse ? errorResponse.message : 'An error occurred while submitting the form.';
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: errorMessage,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showCloseButton: false,
                });
            }
        });
    });
});

$('#generateButton').click(function () {
    // Get the input values
    var packageValue = $('#package').val();
    var shaValue = $('#sha').val();
    var domainValue = $('#domain').val();
    const isValidDomain = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/i.test(domainValue);
    const isValidSha = /^([A-Fa-f0-9]{2}:){31}[A-Fa-f0-9]{2}$/.test(shaValue);
    const isValidPackage = /^[a-zA-Z][a-zA-Z\d_]*(\.[a-zA-Z][a-zA-Z\d_]*)+$/i.test(packageValue);

    if (isValidDomain && isValidSha && isValidPackage) {// Create the data array
        var data = [{
            "relation": ["delegate_permission/common.handle_all_urls"], "target": {
                "namespace": "android_app", "package_name": packageValue, "sha256_cert_fingerprints": [shaValue]
            }
        }, {
            "relation": ["delegate_permission/common.get_login_creds"], "target": {
                "namespace": "web", "site": 'https://' + domainValue
            }
        }, {
            "relation": ["delegate_permission/common.get_login_creds"], "target": {
                "namespace": "android_app", "package_name": packageValue, "sha256_cert_fingerprints": [shaValue]
            }
        }];

        // Convert the data array to a JSON string
        var jsonData = JSON.stringify(data, null, 2);

        // Create a Blob from the JSON string
        var blob = new Blob([jsonData], {type: 'application/json'});

        // Create a download link
        var downloadLink = $('<a></a>')
            .attr('href', URL.createObjectURL(blob))
            .attr('download', 'assetlinks.json');

        // Simulate a click on the download link
        downloadLink[0].click();
    }
});