<?php

namespace App\Console\Commands;


use App\Models\Category;
use App\Models\Project;
use Illuminate\Console\Command;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class GenerateSitemapCommand extends Command
{
	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $signature = 'sitemap:generate';
	
	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Generate the sitemap.';
	
	
	/**
	 * Execute the console command.
	 *
	 * @return void
	 */
	public function handle(): void
	{
		$lastModificationDate = now();
		Sitemap::create()
			->add(
				Url::create(route('index'))->setLastModificationDate(
					$lastModificationDate,
				)->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY,)->setPriority(1),
			)
			->add(
				Url::create(route('contact'))->setLastModificationDate(
					$lastModificationDate,
				)->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY,)->setPriority(0.5),
			)
			->add(
				Url::create(route('projects.index'))->setLastModificationDate(
					$lastModificationDate,
				)->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY,)->setPriority(0.7),
			)
			->add(
				Url::create(route('about'))->setLastModificationDate(
					$lastModificationDate,
				)->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY,)->setPriority(0.7),
			)
			->add(
				Url::create(route('services'))->setLastModificationDate(
					$lastModificationDate,
				)->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY,)->setPriority(0.7),
			)
			->add(
				Url::create(route('tools.index'))->setLastModificationDate(
					$lastModificationDate,
				)->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY,)->setPriority(0.85),
			)
			->add(Project::all())
			->add(Category::all())
			->writeToFile(public_path('sitemap.xml'));
	}
}