<?php

namespace App\Traits;


use Carbon\Carbon;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use CyrildeWit\EloquentViewable\View;
use Illuminate\Database\Eloquent\Builder;

trait HasOverview
{
	public static function count(bool $forceInstanceCount = false, int $limitDays = 30): int
	{
		$hasVisits = in_array(InteractsWithViews::class, class_uses(self::class));
		$dateRange = [
			Carbon::now()->subDays($limitDays)->toDateTimeString(),
			Carbon::now()->toDateTimeString(),
		];
		$query = self::getCountQuery($hasVisits, $dateRange, $forceInstanceCount);
		
		return $query->count();
	}
	
	
	public static function chart(int $limitDays = 30, bool $forceInstanceCount = false): ?array
	{
		$hasVisits = in_array(InteractsWithViews::class, class_uses(self::class));
		$dateFormat = "DATE_FORMAT(%s, '%%d-%%m-%%Y') as date";
		$dateColumn = $hasVisits && !$forceInstanceCount ? 'viewed_at' : 'created_at';
		$dateRange = [
			Carbon::now()->subDays($limitDays)->toDateTimeString(),
			Carbon::now()->toDateTimeString(),
		];
		$query = self::getCountQuery($hasVisits, $dateRange, $forceInstanceCount);
		$column = $hasVisits && !$forceInstanceCount ? "visitor" : 'id';
		
		return $query->selectRaw(sprintf($dateFormat, $dateColumn) . ", COUNT(DISTINCT {$column}) as aggregate")
			->groupBy('date')
			->pluck('aggregate', 'date')
			->toArray();
	}
	
	
	/**
	 * @param bool $hasVisits
	 * @param array|null $dateRange
	 * @param bool $forceInstanceCount
	 * @return mixed
	 */
	public static function getCountQuery(
		bool $hasVisits,
		array $dateRange = null,
		bool $forceInstanceCount = false,
	): Builder {
		if (empty($dateRange)) {
			$dateRange = [
				Carbon::now()->subDays(30)->toDateTimeString(),
				Carbon::now()->toDateTimeString(),
			];
		}
		
		return $hasVisits && !$forceInstanceCount
			? View::where('viewable_type', self::class)
				->whereBetween('viewed_at', $dateRange)
				->distinct('views.visitor')
			: static::query()->whereBetween('created_at', $dateRange);
	}
}