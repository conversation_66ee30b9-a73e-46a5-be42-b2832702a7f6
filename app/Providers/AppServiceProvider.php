<?php

namespace App\Providers;


use App\Models\Configuration;
use App\Models\Project;
use App\Rules\SafeUrlRule;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rule;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
	    Rule::macro('SafeUrl',  function () {
		    return new SafeUrlRule();
	    });
    }

    /**
     * Bootstrap any application services.
     */
	public function boot(): void
	{
		// Temporarily commented out to allow migrations to run
		try {
			$count = Project::count();
			$projectsCount = $count > 0 ? $count : (config('projects.count.projects')??Configuration::firstWhere('key', 'projects_count')?->value);
			view()->share('projectsCount', $projectsCount);
			view()->share('actualProjectsCount', $count);
		} catch (\Exception $e) {
			// Database tables don't exist yet, use default values
			view()->share('projectsCount', 0);
			view()->share('actualProjectsCount', 0);
		}
    }
}
