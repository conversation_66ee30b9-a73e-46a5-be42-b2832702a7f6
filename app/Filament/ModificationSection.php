<?php

namespace App\Filament;


use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;

class ModificationSection
{
	public static function make()
	{
		return Section::make('Modifications')
			->columns(2)
			->schema([
				Placeholder::make('created_at')
					->label('Created Date')
					->content(fn(?Model $record): string => $record?->created_at?->diffForHumans() ?? '-'),
				Placeholder::make('updated_at')
					->label('Last Modified Date')
					->content(fn(?Model $record): string => $record?->updated_at?->diffForHumans() ?? '-'),
			]);
	}
}