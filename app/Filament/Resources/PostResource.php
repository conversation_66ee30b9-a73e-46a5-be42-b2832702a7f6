<?php

namespace App\Filament\Resources;


use App\Filament\ModificationSection;
use App\Filament\Resources\PostResource\Pages;
use App\Models\Post;
use Exception;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\CheckboxColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use GalleryJsonMedia\Form\JsonMediaGallery;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class PostResource extends Resource
{
	protected static ?string $model = Post::class;
	
	protected static ?string $slug = 'posts';
	
	protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	
	protected static ?string $label = "instagram posts";
	
	
	public static function form(Form $form): Form
	{
		return $form->columns(3)
			->schema([
				TextInput::make('title')->required(),
				TextInput::make('action')->label('Call to Action'),
				DatePicker::make('posting_date')->native(false),
				TagsInput::make('hashtags')->nullable(),
				Checkbox::make('published')->default(false),
				Textarea::make('description')->nullable()->rows(5)->columnSpan(3),
				JsonMediaGallery::make('images')
					->directory('instagram')
					->reorderable()
					->image() // only images
					->downloadable()
					->deletable(),
				ModificationSection::make(),
			]);
	}
	
	
	/**
	 * @throws Exception
	 */
	public static function table(Table $table): Table
	{
		return $table
			->columns([
				TextColumn::make('title')->searchable()->sortable()
					->description(fn(Post $post) => Str::limit($post->description, 50)),
				CheckboxColumn::make('published')->sortable(),
				TextColumn::make('hashtags')->hidden()->searchable(),
				TextColumn::make('action')->badge()->searchable(),
			])
			->filters([
				TrashedFilter::make(),
			])
			->actions([
				EditAction::make(),
				DeleteAction::make(),
				RestoreAction::make(),
				ForceDeleteAction::make(),
			])
			->bulkActions([
				BulkActionGroup::make([
					DeleteBulkAction::make(),
					RestoreBulkAction::make(),
					ForceDeleteBulkAction::make(),
				]),
			]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListPosts::route('/'),
			'create' => Pages\CreatePost::route('/create'),
			'edit' => Pages\EditPost::route('/{record}/edit'),
		];
	}
	
	
	public static function getEloquentQuery(): Builder
	{
		return parent::getEloquentQuery()
			->withoutGlobalScopes([
				SoftDeletingScope::class,
			]);
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return ['title', 'hashtags', 'description', 'action'];
	}
}
