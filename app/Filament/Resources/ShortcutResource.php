<?php

namespace App\Filament\Resources;


use App\Filament\ModificationSection;
use App\Filament\Resources\ShortcutResource\Pages;
use App\Models\Shortcut;
use Exception;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ShortcutResource extends Resource
{
	protected static ?string $model = Shortcut::class;
	
	protected static ?string $slug = 'shortcuts';
	
	protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	
	
	public static function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make()->schema([
					TextInput::make('original_url')->required()->url(),
					TextInput::make('shortcut')->required()->url(),
					TextInput::make('visits')->readOnly(true),
				])->columns(3),
				ModificationSection::make(),
			]);
	}
	
	
	/**
	 * @throws Exception
	 */
	public static function table(Table $table): Table
	{
		return $table
			->columns([
				TextColumn::make('original_url')->searchable()->sortable(),
				TextColumn::make('shortcut')->searchable()->sortable(),
				TextColumn::make('visits')->sortable(),
			])
			->filters([
				TrashedFilter::make(),
			])
			->actions([
				EditAction::make(),
				DeleteAction::make(),
				RestoreAction::make(),
				ForceDeleteAction::make(),
			])
			->bulkActions([
				BulkActionGroup::make([
					DeleteBulkAction::make(),
					RestoreBulkAction::make(),
					ForceDeleteBulkAction::make(),
				]),
			]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListShortcuts::route('/'),
			'create' => Pages\CreateShortcut::route('/create'),
			'edit' => Pages\EditShortcut::route('/{record}/edit'),
		];
	}
	
	
	public static function getEloquentQuery(): Builder
	{
		return parent::getEloquentQuery()
			->withoutGlobalScopes([
				SoftDeletingScope::class,
			]);
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return [];
	}
}
