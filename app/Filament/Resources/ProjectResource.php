<?php

namespace App\Filament\Resources;


use App\Filament\Resources\ProjectResource\Pages;
use App\Models\Category;
use App\Models\Project;
use Faker\Provider\Text;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\CheckboxColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ProjectResource extends Resource
{
	protected static ?string $model = Project::class;
	
	protected static ?string $slug = 'projects';
	protected static ?string $navigationIcon = 'heroicon-o-briefcase';
	
	protected static ?string $recordTitleAttribute = 'name';
	
	
	public static function form(Form $form): Form
	{
		return $form->schema([
			Section::make('Project')
				->schema([
					TextInput::make('name')->required(),
					Checkbox::make('published'),
					DatePicker::make('release_date')->native(false),
				])->columns(3),
			Section::make('Publish on Website')->schema([
				TextInput::make('original_url')->url()->nullable(),
				TextInput::make('demo_url')->url()->nullable(),
				SpatieMediaLibraryFileUpload::make('logo')
					->collection('logo'),
				Select::make('Categories')
					->options(Category::pluck('name', 'id')) // Assuming 'name' is the category name column
					->multiple()
					->preload()
					->relationship(name: 'categories', titleAttribute: 'name')
					->placeholder('Select Categories')
					->nullable(),
				MarkdownEditor::make('description')
					->required(),
			]),
			Section::make('Modifications')
				->columns(2)
				->schema([
					Placeholder::make('created_at')
						->label('Created Date')
						->content(fn(?Project $record): string => $record?->created_at?->diffForHumans() ?? '-'),
					Placeholder::make('updated_at')
						->label('Last Modified Date')
						->content(fn(?Project $record): string => $record?->updated_at?->diffForHumans() ?? '-'),
				]),
		]);
	}
	
	
	public static function table(Table $table): Table
	{
		return $table->columns([
			TextColumn::make('name')->searchable()->sortable(),
			TextColumn::make('description'),
			CheckboxColumn::make('published'),
			TextColumn::make('views_count'),
			TextColumn::make('release_date')->date(),
		]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListProjects::route('/'),
			'create' => Pages\CreateProject::route('/create'),
			'edit' => Pages\EditProject::route('/{record}/edit'),
		];
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return ['name'];
	}
}
