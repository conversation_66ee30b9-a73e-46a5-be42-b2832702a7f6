<?php

namespace App\Filament\Resources;


use App\Filament\ModificationSection;
use App\Filament\Resources\ServiceResource\Pages;
use App\Models\Service;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ServiceResource extends Resource
{
	protected static ?string $model = Service::class;
	
	protected static ?string $slug = 'services';
	
	protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	
	
	public static function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make()->schema([
					TextInput::make('name')
						->unique('services', 'name', fn(Service $service) => $service)
						->required(),
					Textarea::make('description')
						->rows(5),
					SpatieMediaLibraryFileUpload::make('icon')
						->collection(Service::COLLECTION)
						->image()
						->imageEditor()
						->imageCropAspectRatio('1:1'),
				]),
				ModificationSection::make(),
			]);
	}
	
	
	/**
	 * @throws \Exception
	 */
	public static function table(Table $table): Table
	{
		return $table
			->columns([
				TextColumn::make('name')
					->searchable()
					->sortable(),
			])
			->filters([
				TrashedFilter::make(),
			])
			->actions([
				EditAction::make(),
				DeleteAction::make(),
				RestoreAction::make(),
				ForceDeleteAction::make(),
			])
			->bulkActions([
				BulkActionGroup::make([
					DeleteBulkAction::make(),
					RestoreBulkAction::make(),
					ForceDeleteBulkAction::make(),
				]),
			]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListServices::route('/'),
			'create' => Pages\CreateService::route('/create'),
			'edit' => Pages\EditService::route('/{record}/edit'),
		];
	}
	
	
	public static function getEloquentQuery(): Builder
	{
		return parent::getEloquentQuery()
			->withoutGlobalScopes([
				SoftDeletingScope::class,
			]);
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return ['name'];
	}
}
