<?php

namespace App\Filament\Resources\ToolResource\Pages;


use App\Filament\Resources\ToolResource;
use App\Filament\Widgets\Dashboard\StateCount;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditTool extends EditRecord
{
	protected static string $resource = ToolResource::class;
	
	
	protected function getHeaderWidgets(): array
	{
		return [StateCount::make(['record' => $this->record])];
	}
	
	
	protected function getHeaderActions(): array
	{
		return [
			DeleteAction::make(),
			ForceDeleteAction::make(),
			RestoreAction::make(),
		];
	}
}
