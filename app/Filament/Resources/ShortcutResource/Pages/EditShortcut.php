<?php

namespace App\Filament\Resources\ShortcutResource\Pages;


use App\Filament\Resources\ShortcutResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditShortcut extends EditRecord
{
	protected static string $resource = ShortcutResource::class;
	
	
	protected function getHeaderActions(): array
	{
		return [
			DeleteAction::make(),
			ForceDeleteAction::make(),
			RestoreAction::make(),
		];
	}
	
	
	public function getHeaderWidgetsColumns(): int|string|array
	{
		return 1;
	}
	
	
	protected function getHeaderWidgets(): array
	{
		if ($this->record) {
			$widgets = [
				ShortcutResource\Widgets\ShortcutVisitChart::make([
					'record' => $this->record,
				]),
			];
		}
		
		return $widgets ?? [];
	}
}
