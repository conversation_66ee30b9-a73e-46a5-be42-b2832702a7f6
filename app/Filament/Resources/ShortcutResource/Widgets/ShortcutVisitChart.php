<?php

namespace App\Filament\Resources\ShortcutResource\Widgets;


use App\Models\Shortcut;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class ShortcutVisitChart extends ChartWidget
{
	public Shortcut $record;
	protected static ?string $heading = 'Visits';
	protected static ?string $pollingInterval = '3s';
	public ?string $filter = 'day';
	protected static ?string $maxHeight = '200px';
	
	
	protected function getData(): array
	{
		$activeFilter = $this->filter ?? 'day';
		$filters = [
			'hour' => ['perHour', 'Day', 'H'],
			'day' => ['perDay', 'Month', 'd'],
			'month' => ['perMonth', 'Year', 'm'],
			'year' => ['perYear', 'Century', 'Y'],
		];
		$filterData = $filters[$activeFilter] ?? $filters['day'];
		$filterStartDate = now()->{"startOf" . $filterData[1]}();
		$filterEndDate = now()->{"endOf" . $filterData[1]}();
		$format = $filterData[2];
		// Generate date range
		$dateRange = [];
		$startDate = Carbon::parse($filterStartDate);
		$endDate = Carbon::parse($filterEndDate);
		while ($startDate->lte($endDate)) {
			$dateRange[] = $startDate->copy()->format($format); // Store the formatted date
			$startDate->{"add" . ucfirst($activeFilter)}(); // Move to the next day
		}
		$data = $this->record->views()
			->selectRaw("DATE_FORMAT(viewed_at, '%{$format}') as date, COUNT(id) as aggregate")
			->whereBetween('viewed_at', [$filterStartDate, $filterEndDate])
			->groupBy('date')
			->get();
		$dataArray = $data->pluck('aggregate', 'date')->all();
		$data = collect($dateRange)->map(function ($date) use ($format, $dataArray) {
			return [
				'date' => $date,
				'aggregate' => $dataArray[$date] ?? 0,
			];
		});
		
		return [
			'datasets' => [
				[
					'label' => 'Link Visits',
					'data' => $data->map(fn($value) => $value['aggregate']),
				],
			],
			'labels' => $data->map(fn($value) => $value['date']),
		];
	}
	
	
	protected function getType(): string
	{
		return 'line';
	}
	
	
	protected function getFilters(): ?array
	{
		return [
			'hour' => 'Today per Hour',
			'day' => 'Today',
			'month' => 'This Month',
			'year' => 'All Time',
		];
	}
}