<?php

namespace App\Filament\Resources;


use App\Filament\Resources\ExperienceResource\Pages;
use App\Filament\Resources\Partials\ModificationSection;
use App\Models\Experience;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ExperienceResource extends Resource
{
	protected static ?string $model = Experience::class;
	
	protected static ?string $slug = 'experiences';
	
	protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	
	
	public static function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make()->schema([
					Grid::make()->schema([
						TextInput::make('name')
							->required(),
						DatePicker::make('start')
							->native(false)
							->required(),
						DatePicker::make('finish')
							->native(false)
							->nullable(),
						TextInput::make('company')
							->nullable(true),
					])->columns(4),
					Grid::make()->schema([
						Textarea::make('description')
							->rows(16)
							->nullable(),
						SpatieMediaLibraryFileUpload::make('logo')
							->panelAspectRatio('4:3')
							->collection(Experience::COLLECTION_LOGO),
					])->columns(2),
				]),
				ModificationSection::make(),
			]);
	}
	
	
	public static function table(Table $table): Table
	{
		return $table
			->columns([
				TextColumn::make('name')
					->searchable()
					->sortable(),
				TextColumn::make('start')
					->date(),
				TextColumn::make('finish'),
				TextColumn::make('company'),
			])
			->filters([
				//
			])
			->actions([
				EditAction::make(),
				DeleteAction::make(),
			])
			->bulkActions([
				BulkActionGroup::make([
					DeleteBulkAction::make(),
				]),
			]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListExperiences::route('/'),
			'create' => Pages\CreateExperience::route('/create'),
			'edit' => Pages\EditExperience::route('/{record}/edit'),
		];
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return ['name'];
	}
}
