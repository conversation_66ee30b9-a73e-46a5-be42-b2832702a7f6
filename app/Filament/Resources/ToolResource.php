<?php

namespace App\Filament\Resources;


use App\Filament\ModificationSection;
use App\Filament\Resources\Partials\ActiveColumn;
use App\Filament\Resources\ToolResource\Pages;
use App\Models\Tool;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class ToolResource extends Resource
{
	protected static ?string $model = Tool::class;
	
	protected static ?string $slug = 'tools';
	
	protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	
	
	public static function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make()->columns(3)->schema([
					Grid::make()
						->columns(1)
						->columnSpan(2)
						->schema([
							TextInput::make('name')->required(),
							TextInput::make('url')->required(),
							Textarea::make('description')->required(),
						]),
					SpatieMediaLibraryFileUpload::make('icon')
						->collection(Tool::COLLECTION_ICON)
						->imageCropAspectRatio('1:1')
						->image()
						->editableSvgs()
						->imageEditor(),
				]),
				ModificationSection::make(),
			]);
	}
	
	
	public static function table(Table $table): Table
	{
		return $table
			->columns([
				SpatieMediaLibraryImageColumn::make('icon')
					->collection(Tool::COLLECTION_ICON),
				TextColumn::make('name')
					->searchable()
					->sortable()
					->description(fn(Tool $tool) => Str::limit($tool->description, 50)),
				TextColumn::make('url')
					->url(fn(Tool $tool) => $tool->url),
				ActiveColumn::make(),
			])
			->filters([
				TrashedFilter::make()
					->native(false),
			])
			->actions([
				EditAction::make(),
			]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListTools::route('/'),
			'edit' => Pages\EditTool::route('/{record}/edit'),
		];
	}
	
	
	public static function getEloquentQuery(): Builder
	{
		return parent::getEloquentQuery()
			->withoutGlobalScopes([
				SoftDeletingScope::class,
			]);
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return ['name'];
	}
}
