<?php

namespace App\Filament\Resources;


use App\Filament\ModificationSection;
use App\Filament\Resources\BadUrlResource\Pages;
use App\Models\BadUrl;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class BadUrlResource extends Resource
{
	protected static ?string $model = BadUrl::class;
	
	protected static ?string $slug = 'bad-url';
	
	protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	
	
	public static function form(Form $form): Form
	{
		return $form
			->schema([
				TextInput::make('url')->required()->url(),
				TextInput::make('type')->required()->nullable(),
				ModificationSection::make()
			]);
	}
	
	
	public static function table(Table $table): Table
	{
		return $table
			->columns([
				TextColumn::make('url'),
				TextColumn::make('type'),
			])
			->filters([
				//
			])
			->actions([
				EditAction::make(),
				DeleteAction::make(),
			])
			->bulkActions([
				BulkActionGroup::make([
					DeleteBulkAction::make(),
				]),
			]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListBadUrls::route('/'),
			'create' => Pages\CreateBadUrl::route('/create'),
			'edit' => Pages\EditBadUrl::route('/{record}/edit'),
		];
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return [];
	}
}
