<?php

namespace App\Filament\Resources;


use App\Filament\Resources\CategoryResource\Pages;
use App\Models\Category;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class CategoryResource extends Resource
{
	protected static ?string $model = Category::class;
	
	protected static ?string $slug = 'categories';
	protected static ?string $navigationIcon = 'heroicon-o-archive-box';
	
	protected static ?string $recordTitleAttribute = 'name';
	
	
	public static function form(Form $form): Form
	{
		
		return $form->schema([
			TextInput::make('name')
				->required()
				->maxLength(255),
			Select::make('Categories')
				->options(Category::pluck('name', 'id')) // Assuming 'name' is the category name column
				->multiple()
				->preload()
				->relationship(name: 'related', titleAttribute: 'name')
				->placeholder('Select Categories')
				->nullable(),
			Section::make('Content')
				->columns(1)
				->schema([
					Textarea::make('description')
						->rows(10)
						->required(),
				]),
			Section::make('Modifications')
				->columns(2)
				->schema([
					Placeholder::make('created_at')
						->label('Created Date')
						->content(fn(?Category $record): string => $record?->created_at?->diffForHumans() ?? '-'),
					Placeholder::make('updated_at')
						->label('Last Modified Date')
						->content(fn(?Category $record): string => $record?->updated_at?->diffForHumans() ?? '-'),
				]),
		]);
	}
	
	
	public static function table(Table $table): Table
	{
		return $table
			->filters([
				SelectFilter::make('Children Of')
					->relationship('related', 'name')
					->options(Category::all()->pluck('name', 'id'))
					->native(false),
			])
			->columns([
				TextColumn::make('name')->searchable()->sortable()->description(
					fn(Category $category) => $category->description,
				),
				TextColumn::make('views_count'),
			]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListCategories::route('/'),
			'create' => Pages\CreateCategory::route('/create'),
			'edit' => Pages\EditCategory::route('/{record}/edit'),
		];
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return ['name'];
	}
}
