<?php

namespace App\Filament\Resources\Partials;


use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Model;

class ActiveColumn
{
	public static function make(string $label = 'active', string $name = 'active', string $column = 'deleted_at')
	{
		return IconColumn::make($name)
			->label(ucwords(__(strtolower($label))))
			->default('')
			->alignCenter()
			->color(function (Model $record) use ($name) {
				$status = $record->{$name};
				
				return is_null($status) ? 'warning' : ($status ? 'success' : 'danger');
			})
			->icon(function (Model $record) use ($name) {
				$status = $record->{$name};
				
				return is_null(
					$status,
				) ? 'heroicon-m-clock' : ($status ? 'heroicon-m-check-circle' : 'heroicon-o-x-circle');
			})
			->sortable(
				true,
				fn($query, string $direction) => $query->orderBy($column, $direction === 'desc' ? 'asc' : 'desc'),
			);
	}
}