<?php

namespace App\Filament\Resources\Partials;


use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;

class ModificationSection
{
	public static function make(bool $isGrid = false)
	{
		$title = __('modifications');
		$make = $isGrid ? Grid::make($title) : Section::make($title);
		
		return $make
			->columns(2)
			->hiddenOn('create')
			->schema([
				Placeholder::make('created_at')
					->label(__('created_at'))
					->content(fn(?Model $record): string => $record?->created_at ?? '-'),
				Placeholder::make('updated_at')
					->label(__('updated_at'))
					->content(fn(?Model $record): string => $record?->updated_at ?? '-'),
			]);
	}
}