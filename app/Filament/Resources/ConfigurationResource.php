<?php

namespace App\Filament\Resources;


use App\Filament\Resources\ConfigurationResource\Pages;
use App\Models\Configuration;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ConfigurationResource extends Resource
{
	protected static ?string $model = Configuration::class;
	
	protected static ?string $slug = 'configurations';
	protected static ?string $navigationIcon = 'heroicon-o-adjustments-vertical';
	protected static ?string $recordTitleAttribute = 'id';
	
	
	public static function form(Form $form): Form
	{
		return $form->schema([
			TextInput::make('key')
				->required(),
			TextInput::make('value')
				->required(),
			Placeholder::make('created_at')
				->label('Created Date')
				->content(fn(?Configuration $record): string => $record?->created_at?->diffForHumans() ?? '-'),
			Placeholder::make('updated_at')
				->label('Last Modified Date')
				->content(fn(?Configuration $record): string => $record?->updated_at?->diffForHumans() ?? '-'),
		]);
	}
	
	
	public static function table(Table $table): Table
	{
		return $table->columns([
			TextColumn::make('key'),
			TextColumn::make('value'),
		]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListConfigurations::route('/'),
			'create' => Pages\CreateConfiguration::route('/create'),
			'edit' => Pages\EditConfiguration::route('/{record}/edit'),
		];
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return [];
	}
}
