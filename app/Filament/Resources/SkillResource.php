<?php

namespace App\Filament\Resources;


use App\Filament\Resources\SkillResource\Pages;
use App\Models\Skill;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class SkillResource extends Resource
{
	protected static ?string $model = Skill::class;
	
	protected static ?string $navigationIcon = 'heroicon-o-paint-brush';
	protected static ?string $slug = 'skills';
	
	protected static ?string $recordTitleAttribute = 'name';
	
	
	public static function form(Form $form): Form
	{
		return $form->schema([
			TextInput::make('name')
				->required(),
			TextInput::make('percent')
				->required()
				->integer()
				->minValue(0)
				->maxValue(100),
			SpatieMediaLibraryFileUpload::make('logo')
				->collection('logo'),
			Placeholder::make('created_at')
				->label('Created Date')
				->content(fn(?Skill $record): string => $record?->created_at?->diffForHumans() ?? '-'),
			Placeholder::make('updated_at')
				->label('Last Modified Date')
				->content(fn(?Skill $record): string => $record?->updated_at?->diffForHumans() ?? '-'),
		]);
	}
	
	
	public static function table(Table $table): Table
	{
		return $table->columns([
			TextColumn::make('name')
				->searchable()
				->sortable(),
			TextColumn::make('percent'),
		]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListSkills::route('/'),
			'create' => Pages\CreateSkill::route('/create'),
			'edit' => Pages\EditSkill::route('/{record}/edit'),
		];
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return ['name'];
	}
}
