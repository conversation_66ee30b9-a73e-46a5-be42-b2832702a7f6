<?php

namespace App\Filament\Resources;


use App\Filament\ModificationSection;
use App\Filament\Resources\ContactResource\Pages;
use App\Models\Contact;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Str;

class ContactResource extends Resource
{
	protected static ?string $model = Contact::class;
	
	protected static ?string $slug = 'contacts';
	
	protected static ?string $navigationIcon = 'heroicon-o-inbox-arrow-down';
	protected static ?string $recordTitleAttribute = 'name';
	
	
	public static function form(Form $form): Form
	{
		return $form->schema([
			Section::make()->columns(3)->schema([
				TextInput::make('name')->required()->readOnly(),
				TextInput::make('email')->required()->readOnly(),
				TextInput::make('phone')->readOnly(),
			]),
			Section::make()->columns(1)->schema([
				TextInput::make('subject')->readOnly(),
				Textarea::make('message')->required()->readOnly()->rows(7),
			]),
			ModificationSection::make(),
		]);
	}
	
	
	public static function table(Table $table): Table
	{
		return $table->columns([
			TextColumn::make('name')
				->searchable()
				->sortable()
				->description(fn(Contact $contact) => $contact->phone),
			TextColumn::make('email')
				->searchable()
				->sortable(),
			TextColumn::make('subject')
				->searchable()
				->description(fn(Contact $contact) => Str::limit($contact->message, 50)),
		])->bulkActions([
			BulkActionGroup::make([
				DeleteBulkAction::make(),
			]),
		]);
	}
	
	
	public static function getPages(): array
	{
		return [
			'index' => Pages\ListContacts::route('/'),
			'create' => Pages\CreateContact::route('/create'),
			'edit' => Pages\EditContact::route('/{record}/edit'),
		];
	}
	
	
	public static function getGloballySearchableAttributes(): array
	{
		return ['name', 'email'];
	}
}
