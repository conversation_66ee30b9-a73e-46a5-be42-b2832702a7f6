<?php

namespace App\Filament\Widgets\Dashboard;


use App\Models\Page;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Model;

class StateCount extends BaseWidget
{
	protected static ?string $pollingInterval = '3s';
	
	public ?Model $record = null;
	
	protected function getStats(): array
	{
		$record = $this->hasProperty('record') ? $this->getPropertyValue('record') : null;
		$chart = ($record ?? Page::class)::chart();
		$count = ($record ?? Page::class)::count();
		
		return [
			Stat::make('Visitors', number_format($count))
				->icon('heroicon-o-eye')
				->chart($chart),
		];
	}
}
