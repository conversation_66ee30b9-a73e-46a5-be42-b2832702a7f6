<?php

namespace App\Filament\Widgets;


use App\Models\Page;
use CyrildeWit\EloquentViewable\View;
use Filament\Support\RawJs;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class PageVisits<PERSON>hart extends ApexChartWidget
{
	/**
	 * Chart Id
	 *
	 * @var string|null
	 */
	protected static ?string $chartId = 'pageVisitsChart';
	
	protected int|string|array $columnSpan = 'full';
	
	
	protected function getHeading(): ?string
	{
		return 'Unique Visitors';
	}
	
	
	protected function getOptions(): array
	{
		$views = View::where('viewable_type', Page::class)
			->selectRaw('DATE(viewed_at) as date, COUNT(DISTINCT visitor) as count')
			->groupBy('date')
			->orderBy('date')
			->get()
			->pluck('count', 'date');
		
		return [
			'chart' => [
				'type' => 'line',
				'height' => 310,
				'zoom' => [
					'enabled' => true,
					'autoScaleYaxis' => true,
				],
				'toolbar' => [
					'autoSelected' => 'zoom',
				],
			],
			'series' => [
				[
					'name' => __('number_of_visitors'),
					'data' => $views->values()->all(),
				],
			],
			'xaxis' => [
				'categories' => $views->keys()->all(),
				'labels' => [
					'style' => [
						'fontFamily' => 'inherit',
					],
				],
			],
			'yaxis' => [
				'labels' => [
					'style' => [
						'fontFamily' => 'inherit',
					],
				],
			],
			'colors' => ['#34d399'],
			'stroke' => [
				'curve' => 'smooth',
				'width' => 3,
			],
		];
	}
	
	
	protected function extraJsOptions(): ?RawJs
	{
		return RawJs::make(
			<<<'JS'
{
    yaxis: {
        labels: {
            formatter: function (val) {
                return val.toFixed(0);
            }
        }
    }
}
JS,
		);
	}
}
