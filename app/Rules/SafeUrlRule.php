<?php

namespace App\Rules;


use App\Models\BadUrl;
use Ariaieboy\LaravelSafeBrowsing\LaravelSafeBrowsing;
use Exception;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SafeUrlRule implements Rule
{
	private string $message = 'The provided URL is not safe.';
	
	
	/**
	 * Determine if the validation rule passes.
	 *
	 * @param string $attribute
	 * @param mixed $value
	 * @return bool
	 */
	public function passes($attribute, $value): bool
	{
		try {
			$url = BadUrl::firstWhere('url', $value);
			if ($url) {
				$type = Str::lower($url->type);
				$this->message = "Invalid link because contains {$type}";
				
				return false;
			}
			$safeBrowsing = new LaravelSafeBrowsing(apiKey: config('laravel-safe-browsing.google.api_key'));
			$result = $safeBrowsing->isSafeUrl(url: $value, returnType: true);
			if (is_string($result)) {
				$type = Str::lower($result);
				BadUrl::updateOrCreate(['url' => $value], ['type' => $type]);
				$this->message = "Invalid link because contains $type";
				
				return false;
			}
			
			return true;
		} catch (Exception $exception) {
			Log::error($exception);
			$this->message = "Failed to check URL!";
			
			return false;
		}
	}
	
	
	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message(): string
	{
		return $this->message;
	}
}