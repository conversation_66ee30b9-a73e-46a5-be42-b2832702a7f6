<?php

namespace App\Http\Resources;


use App\Models\Shortcut;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Shortcut */
class ShortcutResource extends JsonResource
{
	public function toArray(Request $request): array
	{
		return [
			'id' => $this->id,
			'original_url' => $this->original_url,
			'shortcut' => $this->shortcut,
			'url' => $this->url,
		];
	}
}
