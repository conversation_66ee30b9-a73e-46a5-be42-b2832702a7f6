<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ShortcutRequest extends FormRequest
{
	public function authorize(): bool
	{
		return true;
	}
	
	
	public function rules(): array
	{
		return [
			'original_url' => ['required', 'string', 'url', Rule::SafeUrl()],
			'shortcut' => ['nullable', 'string', 'min:3', 'max:15'],
		];
	}
}
