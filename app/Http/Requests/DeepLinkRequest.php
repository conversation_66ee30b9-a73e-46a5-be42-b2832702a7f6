<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;

class DeepLinkRequest extends FormRequest
{
	public function rules(): array
	{
		return [
			'domain' => [
				'required',
				'string',
				'regex:/^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/',
			],
			'package' => ['required', 'string', 'regex:/^[a-zA-Z][a-zA-Z\d_]*(\.[a-zA-Z][a-zA-Z\d_]*)+$/'],
			'sha' => ['required', 'string', 'regex:/^([A-Fa-f0-9]{2}:){31}[A-Fa-f0-9]{2}$/'],
		];
	}
	
	
	public function messages(): array
	{
		return [
			'domain.required' => 'The domain field is required.',
			'domain.regex' => 'The domain must be a valid domain name.',
			'package.required' => 'The package field is required.',
			'package.string' => 'The package must be a string.',
			'package.regex' => 'The package format is invalid.',
			'sha.required' => 'The SHA field is required.',
			'sha.string' => 'The SHA must be a string.',
			'sha.regex' => 'The SHA format is invalid.',
		];
	}
}
