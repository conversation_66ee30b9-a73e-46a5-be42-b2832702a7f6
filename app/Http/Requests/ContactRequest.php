<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;

class ContactRequest extends FormRequest
{
	public function authorize(): bool
	{
		return true;
	}
	
	
	public function rules(): array
	{
		return [
			'name' => ['required', 'min:2'],
			'email' => ['required', 'email', 'max:254'],
			'phone' => ['nullable', 'min:7', 'max:19'],
			'subject' => ['nullable', 'string'],
			'message' => ['required', 'string', 'max:1000', 'min:30'],
			'g-recaptcha-response' => ['required', 'captcha'],
		];
	}
	
	
	public function messages(): array
	{
		$messages = [
			'g-recaptcha-response.required' => 'You must check the reCAPTCHA.',
			'g-recaptcha-response.captcha' => 'Captcha error! try again later or contact site admin.',
		];
		
		return array_merge(parent::messages(), $messages);
	}
}
