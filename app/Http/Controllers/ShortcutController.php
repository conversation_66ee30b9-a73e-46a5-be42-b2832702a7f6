<?php

namespace App\Http\Controllers;


use App\Enums\QuickMessageEnum;
use App\Exceptions\FailedGenerateShortUrlException;
use App\Http\Requests\ShortcutRequest;
use App\Http\Resources\ShortcutResource;
use App\Models\Shortcut;
use App\Models\Tool;
use Exception;
use helpers\Utility;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;

class ShortcutController extends Controller
{
	
	public function store(ShortcutRequest $request)
	{
		$shortUrl = null;
		$message = QuickMessageEnum::FAILED;
		$status = 400;
		try {
			$validated = $request->only(['original_url']);
			$validated['shortcut'] = Str::slug($request->input('shortcut', $this->generateShortUrl()));
			$shortUrl = Shortcut::updateOrCreate($validated);
			$shortUrl = ShortcutResource::make($shortUrl);
			$message = QuickMessageEnum::SUCCESSFULLY;
			$status = 200;
		} catch (Exception $exception) {
			$message = $exception->getMessage();
		}
		
		return $this->manipulate(message: $message, data: $shortUrl, status: $status);
	}
	
	
	public function handle(string $shortcut)
	{
		$shortcut = Shortcut::where('shortcut', $shortcut)->firstOrFail();
		views($shortcut)->record();
		$shortcut->save();
		
		return Redirect::to($shortcut->original_url);
	}
	
	
	/**
	 * @return string
	 * @throws FailedGenerateShortUrlException
	 */
	private function generateShortUrl(): string
	{
		$attempts = 0;
		do {
			$shorten = Utility::GenerateRandomString();
			$attempts += 1;
			if ($attempts > 5) {
				throw new FailedGenerateShortUrlException();
			}
		} while (Shortcut::where('shortcut', $shorten)->exists());
		
		return $shorten;
	}
}
