<?php

namespace App\Http\Controllers;


use App\Http\Requests\ContactRequest;
use App\Models\Contact;

class ContactController extends Controller
{
	public function index()
	{
		return view('website.contact');
	}
	
	
	public function submitContact(ContactRequest $request)
	{
		$contact = new Contact($request->validated());
		$contact->save();
		$status = 200;
		$message = 'Message sent successfully.<br>Thank you!';
		
		return response()->json(['message' => $message], status: $status);
	}
}
