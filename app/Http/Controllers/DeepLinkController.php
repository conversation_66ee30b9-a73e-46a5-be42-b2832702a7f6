<?php

namespace App\Http\Controllers;


use App\Enums\QuickMessageEnum;
use App\Http\Requests\DeepLinkRequest;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use helpers\Utility;

class DeepLinkController extends Controller
{
	public function index()
	{
	
	}
	
	
	public function test(DeepLinkRequest $request)
	{
		$domain = $request->input('domain');
		$package = $request->input('package');
		$sha = $request->input('sha');
		$message = QuickMessageEnum::FAILED;
		$status = 400;
		try {
			$assetLinkJsonUrl = "https://" . $domain . '/.well-known/assetlinks.json';
			$client = new Client();
			$response = $client->get($assetLinkJsonUrl);
			if ($response->getStatusCode() === 200) {
				$contents = $response->getBody()->getContents();
				$jsonData = json_decode($contents, true);
				if ($jsonData) {
					$res = Utility::validateAssetLinks(
						assetLinkJson: $jsonData,
						sha: $sha,
						domain: $domain,
						package: $package,
					);
					if (is_string($res)) {
						$message = $res;
					} elseif ($res) {
						$message = 'Test Passed Successfully !';
						$status = 200;
					}
				} else {
					$message = 'Invalid json format.';
				}
			} else {
				$status = $response->getStatusCode();
				$message = 'Test failed.';
			}
		} catch (Exception $exception) {
			$message = $exception->getMessage();
		} catch (GuzzleException $exception) {
			$message = $exception->getMessage();
		}
		
		return $this->manipulate(message: $message, status: $status);
	}
}
