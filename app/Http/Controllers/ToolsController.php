<?php

namespace App\Http\Controllers;


use App\Http\Requests\ResolveToolRequest;
use App\Models\Tool;

class ToolsController extends Controller
{
	public function index()
	{
		$tools = Tool::get();
		
		return view('website.tools.index', compact('tools'));
	}
	
	
	public function urls()
	{
		$urlsTool = 'url_shorter';
		$data = ['name' => $urlsTool];
		$tool = Tool::where($data)->firstOrCreate($data);
		views($tool)->cooldown(cooldown: 1)->record();
		
		return view('website.tools.url');
	}
	
	
	public function deeplink()
	{
		return view('website.tools.deeplink');
	}
	
	
	public function resolve(ResolveToolRequest $request)
	{
		$this->recordStaticPageVisit();
		$uuid = $request->input('uuid');
		$tool = Tool::whereUuid($uuid)->firstOrFail();
		views($tool)->cooldown(cooldown: 0)->record();
		
		return redirect($tool->url);
	}
}
