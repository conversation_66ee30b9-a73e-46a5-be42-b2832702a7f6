<?php

namespace App\Http\Controllers;


use App\Models\Category;
use App\Models\Configuration;
use App\Models\Experience;
use App\Models\Page;
use App\Models\Project;
use App\Models\Service;
use App\Models\Skill;

class WebsiteController extends Controller
{
	public function pricing()
	{
		return view('pricing');
	}
	public function index()
	{
		self::recordStaticPageVisit();
		
		$clientsCount = config('projects.count.clients') ?? Configuration::firstWhere('key', 'clients_count')->value;
		$experienceYears = Configuration::firstWhere('key', 'years_experience')->value;
		
		return view('website.index', compact('clientsCount', 'experienceYears'));
	}
	
	
	public function project(Project $project)
	{
		self::recordStaticPageVisit();
		
		if (!$project->published) {
			return redirect(route('portfolio.index'));
		}
		views($project)->cooldown(cooldown: 3)->record();
		return view('website.project',compact('project'));
	}
	
	
	public function projects(Category $category = null)
	{
		self::recordStaticPageVisit();
		
		$query = $category?->projects() ?? Project::query();
		$projects = $query->wherePublished(1)->get();
		$categories = Category::get();
		if($category) {
			views($category)->cooldown(cooldown: 3)->record();
		} else {
			$page = Page::query()
				->whereController('WebsiteController')
				->whereMethod('portfolio')
				->firstOrCreate([
					'controller' => 'WebsiteController',
					'method' => 'portfolio',
					'view' => 'website.portfolio',
					'name' => 'Portfolio Website',
				]);
			views($page)->cooldown(cooldown: 1)->record();
		}
		
		return view('website.portfolio', compact('projects', 'categories', 'category'));
	}
	
	
	public function about()
	{
		self::recordStaticPageVisit();
		
		$page = Page::query()
			->whereController('WebsiteController')
			->whereMethod('about')
			->firstOrCreate([
				'method' => 'about',
				'controller' => 'WebsiteController',
				'view' => 'website.about',
				'name' => 'About Website',
			]);
		views($page)->cooldown(cooldown: 1)->record();
		
		$skills = Skill::all();
		$experiences = Experience::all();
		
		return view('website.about', compact('skills', 'experiences'));
	}
	
	
	public function services()
	{
		self::recordStaticPageVisit();
		
		$page = Page::query()
			->whereController('WebsiteController')
			->whereMethod('services')
			->firstOrCreate([
				'method' => 'services',
				'controller' => 'WebsiteController',
				'view' => 'website.services',
				'name' => 'Services Website',
			]);
		views($page)->cooldown(cooldown: 1)->record();
		
		$services = Service::all();
		
		return view('website.services', compact('services'));
	}
	
	
	public function cv()
	{
		self::recordStaticPageVisit();
		return response()->file(public_path('site/baraa_cv.pdf'));
	}
}
