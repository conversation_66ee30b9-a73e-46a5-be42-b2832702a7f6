<?php

namespace App\Http\Controllers;


use App\Enums\QuickMessageEnum;
use App\Models\Page;
use Exception;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
	
	protected function respondWithSuccess($data)
	{
		return $this->manipulate(
			message: QuickMessageEnum::SUCCESSFULLY,
			data: $data,
			status: 200,
		);
	}
	
	
	protected function manipulate($message = null, $data = [], $status = 200, $customName = null)
	{
		$current_model = $customName ?? $this->getCurrentModelName($data);
		$count = is_countable($data) ? count($data) : null;
		if (!is_string($message)) {
			if (is_a($message, QuickMessageEnum::class)) {
				$message = $message->value;
			} elseif ($count > 0) {
				$message = "$count $current_model found!";
			} elseif (is_null($data)) {
				$message = "No Results of $current_model found!";
			} else {
				$message = "Successfully!";
			}
		}
		
		return response()->json(data: [
			'message' => $message ?? 'Empty response, no data provided!',
			'json' => $data,
		], status: $status);
	}
	
	
	/**
	 * @param mixed $data
	 * @return string|null
	 */
	protected function getCurrentModelName(mixed $data): ?string
	{
		$current_model = null;
		if (!empty($data)) {
			if (is_array($data) && isset($data[0]) && is_object($data[0])) {
				// Check if $data is an array, has elements at index 0, and the element is an object
				$current_model = class_basename(get_class($data[0]));
			} elseif (is_object($data)) {
				$current_model = class_basename(get_class($data));
			}
		}
		
		return $current_model;
	}
	
	
	protected function respondWithNoContent()
	{
		return $this->manipulate(
			message: QuickMessageEnum::SUCCESSFULLY,
			data: null,
			status: 204,
		);
	}
	
	
	protected function respondWithError($message)
	{
		return $this->manipulate(
			message: $message,
			data: null,
			status: 400,
		);
	}
	
	
	protected function recordStaticPageVisit(): void
	{
		try {
			$trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2)[1]['function'];
			$controller = Str::afterLast(get_called_class(), '\\');
			$page = Page::query()
				->whereController($controller)
				->whereMethod($trace)
				->firstOrCreate(['method' => $trace, 'controller' => $controller]);
			views($page)->cooldown(1)->record();
		} catch (Exception $exception) {
			Log::log('error', "Failed to record page visit");
			Log::log('error', $exception->getMessage());
		}
	}
	
}
