<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class CategoryProject extends Pivot
{
	protected $fillable = [
		'category_id',
		'project_id',
	];
	
	
	public function project(): BelongsTo
	{
		return $this->belongsTo(Project::class);
	}
	
	
	public function category(): BelongsTo
	{
		return $this->belongsTo(Category::class);
	}
}
