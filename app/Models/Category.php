<?php

namespace App\Models;


use Carbon\Carbon;
use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use RalphJSmit\Laravel\SEO\Support\HasSEO;
use RalphJSmit\Laravel\SEO\Support\SEOData;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sitemap\Tags\Url;

class Category extends Model implements Sitemapable, Viewable
{
	use SoftDeletes, HasFactory, InteractsWithViews, HasSEO;
	
	protected $fillable = [
		'name',
		'description',
	];
	
	
	public function related(): BelongsToMany
	{
		return $this->belongsToMany(Category::class, 'related_category', 'parent_id', 'child_id');
	}
	
	
	public function projects(): BelongsToMany
	{
		return $this->belongsToMany(Project::class);
	}
	
	
	protected function viewsCount(): Attribute
	{
		return Attribute::make(
			get: fn() => $this->views->count(),
		);
	}
	
	
	public function toSitemapTag(): Url|string|array
	{
		return Url::create(route('portfolio.index', ['category' => $this->id]))
			->setLastModificationDate(Carbon::create($this->updated_at))
			->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
			->setPriority(0.8);
	}
	
	
	public function getDynamicSEOData(): SEOData
	{
//		$tags = explode(',', $this->tags ?? []);
		return new SEOData(
			title: $this->name,
			description: $this->description,
			url: route('portfolio.index', ['category' => $this->id]),
//			tags: $tags,
		);
	}
}
