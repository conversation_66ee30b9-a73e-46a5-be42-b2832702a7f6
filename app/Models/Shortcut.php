<?php

namespace App\Models;


use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use RalphJSmit\Helpers\Laravel\Concerns\HasFactory;

class Shortcut extends Model implements Viewable
{
	use SoftDeletes, InteractsWithViews, HasFactory;
	
	
	protected $fillable = [
		'id',
		'original_url',
		'shortcut',
	];
	
	
	protected function url(): Attribute
	{
		$url = route('handle', ['shortcut' => $this->shortcut]);
		
		return Attribute::make(
			get: fn() => $url,
		);
	}
	
	
	protected function visits(): Attribute
	{
		return Attribute::make(
			get: fn() => count($this->views),
		);
	}
}
