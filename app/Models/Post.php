<?php

namespace App\Models;


use GalleryJsonMedia\JsonMedia\Concerns\InteractWithMedia;
use GalleryJsonMedia\JsonMedia\Contracts\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Post extends Model implements HasMedia
{
	use SoftDeletes, InteractWithMedia;
	
	protected $fillable = [
		'title',
		'description',
		'hashtags',
		'action',
		'posting_date',
		'published',
		'images',
	];
	
	protected $casts = [
		'hashtags' => 'array',
		'images' => 'array',
		'posting_date' => 'datetime',
	];
	
	
	protected function getFieldsToDeleteMedia(): array
	{
		return ['images'];
	}
}
