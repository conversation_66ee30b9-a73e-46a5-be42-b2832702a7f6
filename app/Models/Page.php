<?php

namespace App\Models;


use App\Traits\HasOverview;
use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;

class Page extends Model implements HasMedia, Viewable
{
	use InteractsWithMedia, InteractsWithViews, HasOverview;
	
	protected $fillable = [
		'method',
		'controller',
	];
}
