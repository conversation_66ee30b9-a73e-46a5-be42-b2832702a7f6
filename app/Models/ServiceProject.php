<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class ServiceProject extends Pivot
{
	protected $fillable = [
		'service_id',
		'project_id',
	];
	
	
	public function project(): BelongsTo
	{
		return $this->belongsTo(Project::class);
	}
	
	
	public function service(): BelongsTo
	{
		return $this->belongsTo(Service::class);
	}
}
