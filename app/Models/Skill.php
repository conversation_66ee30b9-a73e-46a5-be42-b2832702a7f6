<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Skill extends Model implements HasMedia
{
	use InteractsWithMedia;
	
	protected $fillable = [
		'name',
		'percent'
	];
	
	
	protected function logo(): Attribute
	{
		return Attribute::make(
			get: fn() => $this->getFirstMediaUrl('logo'),
		);
	}
}
