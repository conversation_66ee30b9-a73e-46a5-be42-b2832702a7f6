<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class RelatedCategory extends Pivot
{
	protected $fillable = [
		'parent_id',
		'child_id',
	];
	
	
	public function parent(): BelongsTo
	{
		return $this->belongsTo(Category::class);
	}
	
	
	public function child(): BelongsTo
	{
		return $this->belongsTo(Category::class);
	}
}
