<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Experience extends Model implements HasMedia
{
	use InteractsWithMedia;
	
	const COLLECTION_LOGO = 'logo';
	
	protected $fillable = [
		'start',
		'finish',
		'company',
		'description',
		'name',
	];
	
	protected $casts = [
		'start' => 'date',
		'finish' => 'date',
	];
	
	
	protected function logo(): Attribute
	{
		$url = $this->getFirstMediaUrl(self::COLLECTION_LOGO);
		
		return Attribute::make(
			get: fn() => $url ?: asset('site/images/empty.svg'),
		);
	}
}
