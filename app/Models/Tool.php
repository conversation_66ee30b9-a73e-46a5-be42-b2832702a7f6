<?php

namespace App\Models;


use App\Traits\HasOverview;
use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Concerns\HasUuid;

class Tool extends Model implements Viewable, HasMedia
{
	use SoftDeletes, HasUuid, InteractsWithViews, InteractsWithMedia, HasOverview;
	
	public const COLLECTION_ICON = 'tool icon';
	
	protected $fillable = [
		'uuid',
		'name',
		'description',
		'url',
	];
	
	
	protected function icon(): Attribute
	{
		return Attribute::make(
			get: fn() => $this->getFirstMediaUrl(self::COLLECTION_ICON),
		);
	}
	
	
	protected function active(): Attribute
	{
		return Attribute::make(
			get: fn() => is_null($this->deleted_at), // More explicit check for active status
		);
	}
}
