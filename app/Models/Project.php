<?php

namespace App\Models;


use Carbon\Carbon;
use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use RalphJSmit\Laravel\SEO\Support\HasSEO;
use RalphJSmit\Laravel\SEO\Support\SEOData;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sitemap\Tags\Url;

class Project extends Model implements HasMedia, Sitemapable, Viewable
{
	use HasFactory, InteractsWithMedia, InteractsWithViews, HasSEO;
	
	public const COVER = 'Cover';
	protected $fillable = [
		'id',
		'name',
		'original_url',
		'demo_url',
		'description',
		'release_date',
		'published',
	];
	
	protected $casts = [
		'release_date' => 'date',
	];
	
	
	public function categories(): BelongsToMany
	{
		return $this->belongsToMany(Category::class)->using(CategoryProject::class);
	}
	
	
	public function service(): BelongsToMany
	{
		return $this->belongsToMany(Service::class, 'service_project');
	}
	
	
	protected function cover(): Attribute
	{
		$cover = $this->getFirstMediaUrl(self::COVER);
		$cover = $cover ?: asset('site/images/empty.svg');
		
		return Attribute::make(
			get: fn() => $cover,
		);
	}
	
	
	protected function categoriesCount(): Attribute
	{
		$count = $this->categories->count();
		
		return Attribute::make(
			get: fn() => $count,
		);
	}
	
	
	protected function next(): Attribute
	{
		$next = self::where('id', '>', $this->id)->orderBy('id')->first();
		
		return Attribute::make(
			get: fn() => $next,
		);
	}
	
	
	protected function prev(): Attribute
	{
		$prev = self::where('id', '>', $this->id)->orderBy('id')->first();
		
		return Attribute::make(
			get: fn() => $prev,
		);
	}
	
	
	protected function viewsCount(): Attribute
	{
		return Attribute::make(
			get: fn() => $this->views->count(),
		);
	}
	
	
	public function toSitemapTag(): Url|string|array
	{
		return Url::create(route('portfolio.project', ['project' => $this->id]))
			->setLastModificationDate(Carbon::create($this->updated_at))
			->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
			->setPriority(0.8);
	}
	
	
	public function getDynamicSEOData(): SEOData
	{
//		$tags = explode(',', $this->tags ?? []);
		return new SEOData(
			title: $this->name,
			description: $this->description,
			image: $this->cover,
			url: route('portfolio.project', ['project' => $this->id]),
//			tags: $tags,
		);
	}
}
