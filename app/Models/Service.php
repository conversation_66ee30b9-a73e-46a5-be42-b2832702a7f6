<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Service extends Model implements HasMedia
{
	use SoftDeletes, HasFactory, InteractsWithMedia;
	
	public const COLLECTION = 'LOGO';
	
	protected $fillable = [
		'name',
		'description',
	];
	protected $appends = ['projects_count'];
	
	
	public function projects(): BelongsToMany
	{
		return $this->belongsToMany(Project::class, 'service_project',);
	}
	
	
	protected function projectsCount(): Attribute
	{
		return Attribute::make(
			get: fn() => $this->projects()->count(),
		);
	}
	
	
	protected function logo(): Attribute
	{
		return Attribute::make(
			get: fn() => $this->getFirstMediaUrl('logo'),
		);
	}
}
