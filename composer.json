{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "anhskohbo/no-captcha": "^3.6", "ariaieboy/laravel-safe-browsing": "^1.3", "cyrildewit/eloquent-viewable": "^7.0", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "flowframe/laravel-trend": "^0.1.5", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.10", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "ralphjsmit/laravel-seo": "^1.4", "filipfonal/filament-log-manager": "^2.0", "leandrocfe/filament-apex-charts": "^3.1", "saade/filament-laravel-log": "^3.1", "spatie/laravel-markdown": "^2.4", "spatie/laravel-medialibrary": "^11.0.0", "spatie/laravel-sitemap": "^7.0", "webplusm/gallery-json-media": "^1.2"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["helpers/Utility.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}