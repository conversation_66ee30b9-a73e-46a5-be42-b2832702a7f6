{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@tanstack/react-query": "^5.80.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "three": "^0.158.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "axios": "^1.6.4", "laravel-vite-plugin": "^1.0.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "vite": "^5.4.1"}}